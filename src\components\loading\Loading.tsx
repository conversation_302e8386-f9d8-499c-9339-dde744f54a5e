import React, { memo } from "react";

export type LoadingProps = {
  emptyPage?: boolean;
};

const Loading: React.FC<LoadingProps> = ({ emptyPage }) => {
  return (
    <div className={!emptyPage ? "loading-icon" : "loading-icon-empty-page"} data-testid="loading-icon">
      <img src="/support-a-creator/img/Loading.gif" className="cn-loading" alt="Loading..." />
    </div>
  );
};

export default memo(Loading);
