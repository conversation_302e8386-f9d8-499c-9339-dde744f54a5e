import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { InterestedCreatorFranchisesYouPlayProps } from "@src/pages/interested-creators/franchises-you-play";
import { interestedCreatorPages } from "@src/pages/interested-creators/information";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { FranchisesYouPlayPageLabels } from "@src/server/contentManagement/FranchisesYouPlayPageMapper";
import { InformationPageLabels } from "@src/server/contentManagement/InformationPageMapper";
import InterestedCreator, { InterestedCreators } from "@src/server/interestedCreators/InterestedCreator";
import InterestedCreatorApplicationsHttpClient from "@src/server/interestedCreators/InterestedCreatorApplicationsHttpClient";
import ContentManagementService from "@src/services/ContentManagementService";
import featureFlags from "@src/utils/feature-flags";
import config from "config";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import crypto from "crypto";
import { Information } from "@eait-playerexp-cn/interested-creators-ui";

export default class InterestedCreatorFranchisesYouPlayPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<InterestedCreatorFranchisesYouPlayProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient,
    private readonly page?: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<InterestedCreatorFranchisesYouPlayProps>> {
    const identity = this.identity(req);
    const nucleusId = identity.nucleusId;
    const defaultInterestedCreator = {
      originEmail: identity.email,
      nucleusId,
      userName: identity.username,
      dateOfBirth: identity.dateOfBirth,
      analyticsId: crypto.createHash("sha256").update(nucleusId.toString()).digest("base64"),
      creatorTypes: identity.creatorTypes || []
    };
    const program = identity.programs[0].code;
    const interestedCreatorFromSession = this.hasSession(req, `${program}.interestedCreator`)
      ? this.session(req, `${program}.interestedCreator`) === true
        ? defaultInterestedCreator
        : (this.session(req, `${program}.interestedCreator`) as InterestedCreator & typeof Information)
      : null;

    const interestedCreator = await this.getInterestedCreator(
      nucleusId,
      (interestedCreatorFromSession as unknown) as InterestedCreators
    );
    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "franchisesYouPlay"
    )) as InformationPageLabels & FranchisesYouPlayPageLabels & CommonPageLabels & BreadcrumbPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled(),
        pageLabels
      }
    };
  }

  private async getInterestedCreator(
    nucleusId: number,
    interestedCreator: InterestedCreators
  ): Promise<InterestedCreators | null> {
    if (!config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      return interestedCreator;
    }

    const existingApplicantInformation = await this.applications.forCreatorWithProgram(nucleusId, this.program);
    if (!existingApplicantInformation) {
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.information) {
      interestedCreator = { ...existingApplicantInformation.applicantInformation, ...interestedCreator };
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.creatorTypes) {
      if (
        (interestedCreator.creatorTypes?.length == 0 || interestedCreator.creatorTypes === undefined) &&
        existingApplicantInformation
      ) {
        interestedCreator.creatorTypes = existingApplicantInformation.applicantInformation.creatorTypes;
      }
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.franchises) {
      interestedCreator = {
        ...interestedCreator,
        preferredFranchises: [
          ...interestedCreator.preferredFranchises,
          ...existingApplicantInformation.applicantInformation.preferredFranchises
        ]
      };
      return interestedCreator;
    }

    interestedCreator = {
      ...interestedCreator,
      createdDate: existingApplicantInformation.createdDateformattedWithoutTime(this.currentLocale)
    };

    return interestedCreator;
  }
}
