import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { Information } from "@eait-playerexp-cn/interested-creators-ui";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { InformationProps, interestedCreatorPages, PageLabels } from "@src/pages/interested-creators/information";
import { FacebookPage } from "@src/server/channels/ConnectedAccountsHttpClient";
import { AddContentPageLabels } from "@src/server/contentManagement/AddContentPageMapper";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/server/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/server/contentManagement/ConnectAccountsPageMapper";
import { InformationPageLabels } from "@src/server/contentManagement/InformationPageMapper";
import InterestedCreator, { InterestedCreators } from "@src/server/interestedCreators/InterestedCreator";
import InterestedCreatorApplicationsHttpClient from "@src/server/interestedCreators/InterestedCreatorApplicationsHttpClient";
import ContentManagementService from "@src/services/ContentManagementService";
import featureFlags from "@src/utils/feature-flags";
import config from "config";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class InterestedCreatorInformationPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<InformationProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient,
    private readonly page?: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<InformationProps & PageLabels>> {
    const identity = this.identity(req);
    const nucleusId = identity.nucleusId;
    const defaultInterestedCreator = {
      originEmail: identity.email,
      nucleusId,
      userName: identity.username,
      dateOfBirth: identity.dateOfBirth
    };
    const program = identity.programs[0].code;
    const interestedCreatorFromSession = this.hasSession(req, `${program}.interestedCreator`)
      ? this.session(req, `${program}.interestedCreator`) === true
        ? defaultInterestedCreator
        : (this.session(req, `${program}.interestedCreator`) as InterestedCreator & typeof Information)
      : null;

    const interestedCreator = await this.getInterestedCreator(
      nucleusId,
      (interestedCreatorFromSession as unknown) as InterestedCreators
    );
    if (!interestedCreator) return { notFound: true };

    const pages = this.hasSession(req, `${this.program}.fbPages`)
      ? (this.session(req, "fbPages") as FacebookPage[])
      : [];

    const pageLabels = (await this.contents.getPageLabels(this.currentLocale, "information")) as AddContentPageLabels &
      BreadcrumbPageLabels &
      CommunicationPreferencesPageLabels &
      CommonPageLabels &
      InformationPageLabels &
      ConnectAccountsPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled(),
        pageLabels,
        pages
      }
    };
  }

  private async getInterestedCreator(
    nucleusId: number,
    interestedCreator: InterestedCreators
  ): Promise<InterestedCreators | null> {
    if (!config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      return interestedCreator;
    }

    const existingApplicantInformation = await this.applications.forCreatorWithProgram(nucleusId, this.program);
    if (!existingApplicantInformation) {
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.information) {
      interestedCreator = { ...existingApplicantInformation.applicantInformation, ...interestedCreator };
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.creatorTypes) {
      if (
        (interestedCreator.creatorTypes?.length == 0 || interestedCreator.creatorTypes === undefined) &&
        existingApplicantInformation
      ) {
        interestedCreator.creatorTypes = existingApplicantInformation.applicantInformation.creatorTypes;
      }
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.franchises) {
      interestedCreator = {
        ...interestedCreator,
        preferredFranchises: [
          ...interestedCreator.preferredFranchises,
          ...existingApplicantInformation.applicantInformation.preferredFranchises
        ]
      };
      return interestedCreator;
    }

    interestedCreator = {
      ...interestedCreator,
      createdDate: existingApplicantInformation.createdDateformattedWithoutTime(this.currentLocale)
    };

    return interestedCreator;
  }
}
