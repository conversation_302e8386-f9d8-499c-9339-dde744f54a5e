.faq-page-wrapper {
  @apply flex w-full flex-col items-center justify-center bg-navy-80 text-white;
}
.faq-page-header-section {
  @apply flex h-[275px] w-full 
    w-full flex-col items-center gap-meas16 bg-[image:var(--header-mobile-background)] bg-cover pb-[28px] pt-[60px] 
    md:h-[266px] md:gap-meas6 md:bg-[image:var(--header-tablet-background)] md:pt-[90px] 
   lg:bg-[image:var(--header-desktop-background)] lg:bg-right lg:pt-[70px];
}
.faq-page-heading {
  @apply text-center font-display-bold text-mobile-display font-bold  md:text-tablet-display lg:text-desktop-display;
}
.faq-page-subHeading {
  @apply w-[238px] text-center font-display-bold text-tablet-h5 font-bold md:w-full;
}
.faq-page-content-container {
  @apply block flex w-[285px] flex-col gap-meas16 py-meas23 md:w-[728px] lg:w-[790px];
}
.faq-page-content-title {
  @apply pb-meas16 text-left font-display-bold text-mobile-display-large font-bold text-custom-9 md:text-desktop-h2;
}
.faq-page-subContent-wrapper {
  @apply flex flex-col gap-meas16;
}
.faq-page-subContent {
  @apply flex flex-col justify-start gap-meas10;
}
.faq-page-question {
  @apply font-display-bold text-mobile-h1 font-bold text-gray-10 md:text-desktop-h3;
}
.faq-page-answer {
  @apply font-text-regular text-desktop-body-large font-normal text-alpha-white-80;
}
.faq-page-border {
  @apply border-b border-white border-opacity-[0.33] pb-meas16;
}
