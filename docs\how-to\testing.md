---
currentMenu: howto
---

# Write effective tests

How to:

- [Add configuration to run before each test file](#add-configuration-to-run-before-each-test-file)
- [Test files structure](#test-files-structure)
- [Test case structure for simple React components](#test-case-structure-for-simple-react-components)
- [Test case structure for complex React components](#test-case-structure-for-complex-react-components)
- [Working with props in a test](#working-with-props-in-a-test)
- [Mock Node modules](#mock-node-modules)
- [Recommendations to query elements in a test](#recommendations-to-query-elements-in-a-test)
- [Removing loops from tests](#removing-loops-from-tests)
- [What to verify on mock calls](#what-to-verify-on-mock-calls)

## Add configuration to run before each test file

It is possible to configure Jest before each test file in the suite is executed.
Please modify the file [setupTests.js](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/setupTests.js) if you need to do things like adding matchers.

The example below extends `expect` with a new matcher for [accessibility](https://github.com/nickcolley/jest-axe)

```js
import "jest-axe/extend-expect";
```

That will save you from adding the same code in **all** your tests as shown below.

```ts
import { toHaveNoViolations } from "jest-axe";

expect.extend(toHaveNoViolations);
```

## Test files structure

Tests files should have the following structure.

```ts
// imports

// Modules mocks setup

describe("ComponentName", () => {
  // Shared variables for all your examples

  // beforeEach, if needed

  // Examples
  it("does something under given circumstances", () => {});

  // ....
});
```

See how each block is separated by an empty line.
Organizing your tests in the same manner will help your team understand them better.

## Test case structure for simple React components

A test case should have the following structure, when your "_arrange_" section is short.
Think, for instance, of a component where you just want to verify its state after rendering.

1. Local variables
2. Empty line (to indicate the end of your `given`/`arrange` section)
3. Call to render
4. Empty line (to indicate the end of your `when`/`act` section)
5. All your assertions

<!-- prettier-ignore-start -->
```ts
it("explains a behavior with a short arrange section", () => {
  // local variables declarations

  // render (what you want to verify)

  // assertions
});
```
<!-- prettier-ignore-end -->

See how each block is separated by an empty line.
Organizing your tests in the same manner will help your team understand them better.

It might be the case that the only setup you need is to provide prop values; in such scenario your test will have only two sections as shown below.

<!-- prettier-ignore-start -->
```ts
it("explains a behavior with a short arrange section", () => {
  // render (what you want to verify)

  // assertions
});
```
<!-- prettier-ignore-end -->

## Test case structure for complex React components

A test case should have the following structure, when your "_arrange_" section is long.
Think for instance a page or a form component with multiple API calls and more than one user interaction.

1. Local variables and mock configuration
2. Call to render
3. Other interactions before the one you want to verify
4. Empty line (to indicate the end of your given/arrange section)
5. Your act/when method call
6. Empty line (to indicate that the assert/then section will start)
7. All your assertions

<!-- prettier-ignore-start -->
```ts
it("explains a behavior with a long arrange section", () => {
  // local variables declarations, and mocks configurations
  // render
  // other actions before your main action (what you need to do as part of your setup)

  // actual action that produces the effect we want to verify

  // assertions
});
```
<!-- prettier-ignore-end -->

See how each block is separated by an empty line.
Organizing your tests in the same manner will help your team understanding them better.

## Working with props in a test

It's recommended to have a single variable for all your component props.
We do this in order to make easier to only override values relevant to the current test case.

An example for our naming conventions to this regard is as follows

- Component: `InterestedCreatorInformationForm`
- Props type: `InterestedCreatorInformationFormProps`
- Variable for props in your test: `interestedCreatorInformationFormProps`

For instance

```tsx
describe("InterestedCreatorsInformationForm", () => {
  const interestedCreatorInformationFormProps = {
    // all your default values here...
  };

  it("describes something relevant to 'interestedCreator' prop", () => {
    const interestedCreatorWithNoValues = {
      // values to be overriden in this test..
    };

    render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationFormProps}
        interestedCreator={interestedCreatorWithNoValues}
      />
    );

    // assertions when `interestedCreator` has specific values
  });
});
```

Overriding props value in this way has the advantage that you don't need to add logic to a `beforeEach` function, since all the default values remain untouched for all your other tests.

## Mock Node modules

If you need to mock a third party module, you can do it via `jext.mock("path-to-module")`.

It is recommended to do this before your `describe` function.
Jest hoists `jest.mock` so when you call `import` it can load the mock, instead of the actual implementation.
Calling `jest.mock` within `describe` or `it` will prevent this behavior.

The snippet below shows the recommended structure for your mocks.

```ts
// All your imports..

jest.mock("next/router");
// other mocks you might need

describe("Component", () => {
  // All of your tests for a component
});
```

`jest.mock` will provide a mock implementation for each export in the module you're mocking.
The following snippets are equivalent.

```ts
// The only function `next/router` exports is `useRouter`
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

// The code above is equivalent to the following line, since Jest will provide a mock for every exported function
jest.mock("next/router");
```

If you need to provide specific functionality for your mocked functions you can do it as shown below.

<pre data-line="1,5,11"><code class="language-ts line-numbers">
const router = { push: jest.fn() };

it("executes router.push (oversimplified name for this example)", async () => {
    // ...
    useRouter.mockImplementation(() => router);
    // ...

    await userEvent.click(link);

    await waitFor(async () => {
      expect(router.push).toHaveBeenCalledWith("/path-to-page");
    });
});
</code></pre>

### Mocking Amplitude object

In order to accidentally introduce errors we **don't recommend to mock all the methods** in the `Amplitude` object.
If your component or page is expecting to only receive specific calls, we'll only provide mocks for those calls, as shown in the example below.

```ts
jest.mock("../../../src/ampli/index", () => {
  return {
    ampli: {
      identify: jest.fn(),
      clickedFooterLink: jest.fn()
    }
  };
});

// Then we would assert calls to that object were made as we expected it

expect(ampli.identify).toHaveBeenCalledWith(undefined, { Locale: locale });
expect(ampli.clickedFooterLink).toHaveBeenCalledWith({
  "Link Clicked": url
});
```

If new code introduces new calls or removes/updates existing calls then your test(s) should fail.

## React testing library best practices

We should avoid [common mistakes with React Testing Library](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library).
We use the [Testing Library eslint plugin](https://github.com/testing-library/eslint-plugin-testing-library) to prevent said common issues.

Most issues should be highlighted by your Editor/IDE, if you need to you can run the linting run rules from your terminal as follows

```shell
npm run lint:tests
```

The above command will output all the linting violations found in your code.

Same linting rules will be applied in a pre-commit hook via `lint-staged`.

## Recommendations to query elements in a test

Your tests must **closely resemble how your web pages are used**.
The following are guidelines on how to best use [query methods](https://testing-library.com/docs/queries/about/) from React Testing Library

This section is a subset of the rules recommended by React Testing Library in their [Query Priority Guidelines](https://testing-library.com/docs/queries/about/#priority).
This section also adds a few examples for the most common use cases.

### Use `byRole` when you want to trigger events

Please use `byRole` query methods if you need to click an element to produce an event.
That will clarify what type of element we're looking for.
The snippet below finds a `button` with the label `label` in order to click on it.

```ts
await userEvent.click(screen.getByRole("button", { name: label }));
```

The property `name` from the example above refers to the [accessible name](https://www.w3.org/TR/accname-1.1/) of your HTML element, and it's usually its label.

### Use `queryBy` when you want to confirm something is not there

Use `queryBy` methods when your goal is to verify that something was removed from the screen.
Once common case is checking that a validation message is removed once a valid value is provided.

```ts
expect(screen.queryByText("Enter a valid URL")).not.toBeInTheDocument();
```

This is the only method that will let you do such thing since `findBy` and `getBy` will throw exceptions if nothing is found.
`queryBy` will return `null` in case the element is not present.

## Removing loops from tests

Sometimes it's inevitable to want to verify the same behavior for multiple elements of the same type.
In those scenarios, use Jest [test.each](https://jestjs.io/docs/api#testeachtablename-fn-timeout) instead of a loop in your test.
`test.each` allows you to write the test once and pass data to it.

The example below shows how we create a test that verifies that clicking on a link takes you to another page, for all the labels and URLs in the `footerLinks` variable.

```tsx
const footerLinks = [
  ["How it Works", "/how-it-works"],
  ["Perks & Rewards", "/opportunities-rewards"]
];

test.each(footerLinks)("navigates to '%s' page", async (label, url) => {
  const push = jest.fn();
  useRouter.mockImplementation(() => ({ push }));
  render(<Footer />);

  await userEvent.click(screen.getByRole("button", { name: label }));

  await waitFor(() => expect(push).toHaveBeenCalledWith(url));
});
```

The snippet above will produce the following output when run

```
Footer

✓ navigates to 'How it Works' page
✓ navigates to 'Perks & rewards' page
```

## What to verify on mock calls

Please check at a minimum the following things, when you're using a `jest.mock` to explain the interaction between collaborators in your tests.

- How many times a function/method was called
- What where the arguments used to call your function method

For instance:

```ts
expect(mockGetContent).toHaveBeenCalledTimes(1);
expect(mockGetContent).toHaveBeenCalledWith(creatorId, participationId, pageSize, pageNumber);
```
