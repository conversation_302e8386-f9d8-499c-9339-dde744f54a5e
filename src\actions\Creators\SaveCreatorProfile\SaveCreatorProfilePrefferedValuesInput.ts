import CommunicationPreferencesInput, { CommunicationPreferencesFormValues } from "./CommunicationPreferencesInput";
import PreferredFranchisesInput, { PreferredFranchisesFormValues } from "./PreferredFranchisesInput";
import MailingAddressInput, { MailingAddressFormValues } from "../MailingAddressInput";
import PreferredPlatformsInput, { PreferredPlatformsFormValues } from "./PreferredPlatformsInput";
import CreatorsTypeInput, { CreatorsTypeFormValues } from "./CreatorsTypeInput";
import AdditionalInformationInput, { AdditionalInformationFormValues } from "./AdditionalInformationInput";
import LegalEntityInformationInput, { LegalEntityInformationFormValues } from "./LegalEntityInformationInput";
import AccountInformationPrefferedValuesInput, {
  AccountInformationPrefferedValues
} from "../AccountInformationPrefferedValuesInput";
import User from "@src/shared/authentication/User";

export default class SaveCreatorProfilePrefferedValuesInput {
  public id?: string;
  readonly creatorTypes?: Array<string> = null;
  readonly accountInformation?: AccountInformationPrefferedValuesInput = null;
  readonly mailingAddress?: MailingAddressInput = null;
  readonly communicationPreferences?: CommunicationPreferencesInput = null;
  readonly additionalInformation?: AdditionalInformationInput = null;
  readonly preferredPlatforms: Array<{ id: string; type: string }> = [];
  readonly preferredFranchises: Array<{ id: string; type: string }> = [];
  readonly legalInformation?: LegalEntityInformationInput;
  readonly creatorConnectedProgram: string;
  readonly creatorSocialLinks: Array<string> = [];

  constructor(
    creator: User,
    values: {
      readonly accountInformation: AccountInformationPrefferedValues;
      readonly mailingAddress: MailingAddressFormValues;
      readonly legalInformation: LegalEntityInformationFormValues;
      readonly additionalInformation: AdditionalInformationFormValues;
      readonly information: AccountInformationPrefferedValues & MailingAddressFormValues & PreferredPlatformsFormValues;
      readonly franchisesYouPlay: PreferredFranchisesFormValues;
      readonly platformPreferences: PreferredPlatformsFormValues;
      readonly creatorTypes: CreatorsTypeFormValues;
      readonly communicationPreferences: CommunicationPreferencesFormValues;
      readonly creatorConnectedProgram: string;
      readonly creatorSocialLinks: Array<string>;
    },
    readonly registerCode: string = null
  ) {
    this.id = creator.id || null;

    if (values.accountInformation !== undefined) {
      this.accountInformation = new AccountInformationPrefferedValuesInput(creator, values.accountInformation);
    }
    if (values.mailingAddress !== undefined) {
      this.mailingAddress = new MailingAddressInput(values.mailingAddress);
    }
    if (values.additionalInformation !== undefined) {
      this.additionalInformation = new AdditionalInformationInput(values.additionalInformation);
    }
    if (values.legalInformation !== undefined) {
      this.legalInformation = new LegalEntityInformationInput(values.legalInformation);
    }
    if (values.information !== undefined) {
      this.accountInformation = new AccountInformationPrefferedValuesInput(creator, values.information);
      this.mailingAddress = new MailingAddressInput(values.information);
      this.preferredPlatforms = new PreferredPlatformsInput(values.information).values;
    }
    if (values.platformPreferences !== undefined) {
      this.preferredPlatforms = new PreferredPlatformsInput(values.platformPreferences).values;
    }

    if (values.creatorTypes !== undefined) {
      this.creatorTypes = new CreatorsTypeInput(values.creatorTypes).values;
    }

    if (values.franchisesYouPlay !== undefined) {
      this.preferredFranchises = new PreferredFranchisesInput(values.franchisesYouPlay).values;
    }

    if (values.communicationPreferences !== undefined) {
      this.communicationPreferences = new CommunicationPreferencesInput(values.communicationPreferences);
    }

    if (values.creatorConnectedProgram !== undefined) {
      this.creatorConnectedProgram = values.creatorConnectedProgram;
    }

    if (values.creatorSocialLinks !== undefined) {
      this.creatorSocialLinks = values.creatorSocialLinks;
    }
  }

  isNew(): boolean {
    return this.id === null;
  }

  setId(id: string): void {
    this.id = id;
  }
}
