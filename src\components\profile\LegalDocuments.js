import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Tabs } from "../tabs";
import Pen from "../icons/Pen.js";
import Loading from "../Loading";
import FormTitle from "../formTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import { ERROR, onToastClose, toastContent, VALIDATION_ERROR } from "../../utils";
import { useRouter } from "next/router";
import LegalDocumentsService from "../../services/LegalDocumentsService";
import { useDependency } from "@src/context/DependencyContext";

const MODE = "horizontal";
const DATEFORMAT = "MMM DD, YYYY";

const NoContracts = ({ legalDocumentsLabels, colSpan }) => {
  return (
    <tr className="no-contracts-container">
      <td colSpan={colSpan}>
        <section className="no-contracts">
          <Pen />
          <div className="no-contract-content">
            <h6>{legalDocumentsLabels.noContractTitle}</h6>
            <p>{legalDocumentsLabels.noContractMessage}</p>
          </div>
        </section>
      </td>
    </tr>
  );
};

const OpportunityContract = ({ data = [], headers = [], legalDocumentsLabels, locale }) => {
  return (
    <div className="opportunity-contract">
      <OpportunityTable {...{ data, headers, legalDocumentsLabels, locale }} />
    </div>
  );
};

const CreatorAgreement = ({ data = [], headers = [], prefix = "", legalDocumentsLabels, locale }) => {
  return (
    <div className="creator-agreement">
      <ContractTable {...{ data, headers, prefix, legalDocumentsLabels, locale }} />{" "}
    </div>
  );
};

const ContractTable = ({ data, headers, prefix, legalDocumentsLabels, locale }) => {
  return (
    <table>
      <thead>
        <tr>
          {headers.map((v, i) => (
            <th key={`head-${i}`}>{v}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {(data.length &&
          data.map((d, i) => {
            return (
              <tr key={`data-${i}`}>
                <td>
                  <a target="_blank" rel="noreferrer" href={d.documentUrl}>
                    {prefix}
                  </a>
                </td>
                <td>{d.acceptedOnDate.format(DATEFORMAT, locale)}</td>
              </tr>
            );
          })) || <NoContracts {...{ legalDocumentsLabels }} colSpan={headers.length} />}
      </tbody>
    </table>
  );
};

const OpportunityTable = ({ data, headers, legalDocumentsLabels, locale }) => {
  return (
    <table>
      <thead>
        <tr>
          {headers.map((v, i) => (
            <th key={`head-${i}`}>{v}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {(data.length &&
          data.map((d, i) => (
            <tr key={`data-${i}`}>
              <td>
                <a
                  href={d.documentUrl}
                  target="_blank"
                  rel="noreferrer"
                  title={d.uploadedByName ? `Uploaded by ${d.uploadedByName}` : ""}
                >
                  {d.uploadedByName ? <span>Signed outside Creator Network</span> : d.label}
                </a>
              </td>
              <td>{d.opportunityName}</td>
              <td>{d.signedOnDate.format(DATEFORMAT, locale)}</td>
            </tr>
          ))) || <NoContracts {...{ legalDocumentsLabels }} />}
      </tbody>
    </table>
  );
};

export default memo(function LegalDocuments({ legalDocumentsLabels, layout }) {
  const { errorHandler } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const [activeTabId, setActiveTab] = useState("opportinuty-contract");
  const [documents, setDocuments] = useState({ contracts: [], history: [] });
  const [isLoader, setIsLoader] = useState(false);
  const { dispatch, state: { isValidationError, validationErrors, isError } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const router = useRouter();

  useEffect(() => {
    setIsLoader(true);
    async function fetchData() {
      try {
        const result = await LegalDocumentsService.geDocumentsWithSignature();
        setDocuments(result.data);
        setIsLoader(false);
      } catch (error) {
        errorHandler(stableDispatch, error);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(validationErrors)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const tabs = useMemo(() => {
    return [
      {
        title: `${legalDocumentsLabels.opportunityContract} (${documents.contracts.length})`,
        id: "opportinuty-contract",
        content: (
          <OpportunityContract
            data={documents.contracts}
            headers={JSON.parse(legalDocumentsLabels.opportunityHeaders)}
            locale={router.locale}
            {...{ legalDocumentsLabels }}
          />
        )
      },
      {
        title: `${legalDocumentsLabels.creatorAgreement} (${documents.history.length})`,
        id: "creator-agreement",
        content: (
          <CreatorAgreement
            data={documents.history}
            headers={JSON.parse(legalDocumentsLabels.contractsHeaders)}
            prefix={legalDocumentsLabels.creatorAgreement}
            locale={router.locale}
            {...{ legalDocumentsLabels }}
          />
        )
      }
    ];
  }, [documents, legalDocumentsLabels]);

  return (
    <div className="legal-documents">
      <FormTitle title={legalDocumentsLabels.pageTitle} />
      {(isLoader && (
        <div className="loader">
          <Loading />
        </div>
      )) || <Tabs {...{ tabs, activeTabId, setActiveTab, mode: MODE }} />}
    </div>
  );
});
