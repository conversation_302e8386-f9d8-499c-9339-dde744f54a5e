import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useEffect } from "react";
import { DateInput, Input, Select } from "@eait-playerexp-cn/core-ui-kit";
import UpdateProfilePicture from "../upload/UpdateProfilePicture";
import PrimaryPlatform from "./PrimaryPlatform";
import CreatorForm from "../FormRules/CreatorForm";
import { useRouter } from "next/router";
import { isAdult } from "../../utils";
import { useDependency } from "../../context/DependencyContext";

const InformationInputs = memo(function InformationInputs({
  labels,
  user,
  futureCreator,
  creator,
  countries,
  platforms = [],
  stableDispatch
}) {
  const methods = useFormContext();
  const { control, setValue, setError } = methods;
  const rules = CreatorForm.rules(labels);
  const router = useRouter();
  const {
    configuration: { FLAG_PER_PROGRAM_PROFILE }
  } = useDependency();

  useEffect(() => {
    setValue("country", creator?.mailingAddress?.country || countries[0]);
  }, [countries]);

  return (
    <>
      <div className="mg-form-container">
        <div className="information-form">
          <div className="information">
            <h4 className="information-title">{labels.infoTitle}</h4>
            {creator?.accountInformation?.needsMigration && (
              <div className="information-subtitle">
                {labels.infoSubTitle} {creator.formattedRegistrationDate(router.locale)}{" "}
              </div>
            )}
          </div>
          <div className="information-avatar">
            {!futureCreator && (
              <UpdateProfilePicture
                src={user.avatar || null}
                user={user}
                labels={labels}
                stableDispatch={stableDispatch}
              />
            )}
          </div>
          <Controller
            control={control}
            name="firstName"
            rules={rules.firstName}
            defaultValue={creator.accountInformation.firstName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.firstName}
                placeholder={labels.labels.firstName}
                id="firstName"
              />
            )}
          />
          <Controller
            control={control}
            name="lastName"
            rules={rules.lastName}
            defaultValue={creator.accountInformation.lastName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.lastName}
                placeholder={labels.labels.lastName}
                id="lastName"
              />
            )}
          />
          <div>
            <div className="input-box-label">{labels.labels.EAID}</div>
            <div className="information-field">{creator.accountInformation.defaultGamerTag}</div>
          </div>
          <div>
            <div className="input-box-label">{labels.labels.EAEmail}</div>
            <div className="information-field">{creator.accountInformation.originEmail}</div>
          </div>
          <Controller
            control={control}
            name="dateOfBirth"
            rules={rules.dateOfBirth}
            defaultValue={creator.dateOfBirth()}
            render={({ field, fieldState: { error } }) => (
              <DateInput
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.dateOfBirth}
                placeholder={labels.labels.dateOfBirth}
                locale={router.locale}
                maxDate={new Date()}
                title={labels.header.calendar}
                cancelText={labels.buttons.cancel}
                okText={labels.buttons.ok}
                onCancel={(date) => {
                  if (isAdult(date)) {
                    setError(
                      "dateOfBirth",
                      { type: "manual", message: labels.messages.ageMustBe18OrOlder },
                      { shouldFocus: true }
                    );
                  } else {
                    setError("dateOfBirth", null);
                  }
                  setValue("dateOfBirth", date);
                }}
              />
            )}
          />
          <Controller
            control={control}
            name="country"
            rules={rules.country}
            defaultValue={creator?.mailingAddress?.country}
            render={({ field, fieldState: { error } }) => (
              <Select
                id="country-information"
                selectedOption={creator?.mailingAddress?.country}
                errorMessage={error && error.message}
                onChange={(item) => {
                  field.onChange(item);
                }}
                options={countries}
                label={labels.labels.country}
                dark
              />
            )}
          />
          <Controller
            control={control}
            name="street"
            rules={rules.street}
            defaultValue={creator?.mailingAddress?.street}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.street}
                placeholder={labels.labels.street}
              />
            )}
          />
          <Controller
            control={control}
            name="city"
            rules={rules.city}
            defaultValue={creator?.mailingAddress?.city}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.city}
                placeholder={labels.labels.city}
              />
            )}
          />
          <Controller
            control={control}
            name="state"
            rules={rules.state}
            defaultValue={creator?.mailingAddress?.state}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.state}
                placeholder={labels.labels.state}
              />
            )}
          />
          <Controller
            control={control}
            name="zipCode"
            rules={rules.zipCode}
            defaultValue={creator?.mailingAddress?.zipCode}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={labels.labels.zipCode}
                placeholder={labels.labels.zipCode}
              />
            )}
          />
        </div>
      </div>
      {platforms && (
        <PrimaryPlatform
          {...{
            primaryPlatform: FLAG_PER_PROGRAM_PROFILE
              ? creator.preferredPrimaryPlatform
              : creator.preferredPrimaryPlatforms,
            secondaryPlatforms: creator.preferredSecondaryPlatforms || [],
            options: platforms,
            infoLabels: labels
          }}
        />
      )}
    </>
  );
});

export default InformationInputs;
