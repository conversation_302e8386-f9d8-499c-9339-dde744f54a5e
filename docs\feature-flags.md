---
currentMenu: flags
---

# Feature Flags

## FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED

This flag allows creators to submit new content types from Instagram: Photos and Carousel photos

It affects the following logic

- Uses [new endpoint](https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/saveSocialMediaContentsWithInstagramMultipleMediaSupport) to submit content
- Updates error message for non supported content types
- Updates help text to inform abut the new supported types

## FLAG_COUNTRIES_BY_TYPE

This flag allows to fetch configured countries and all the countries based on the value passed in query parameter "type".

- Uses [new endpoint](https://eait-playerexp-cn.gitlab.ea.com/cn-services/metadata-api/docs/api.html#tag/Metadata/operation/getConfiguredCountries) to fetch the list of countries.

It affects the following pages

- `my-profile`, `interested-creator` and `onboarding`.

## FLAG_INTERESTED_CREATOR_CAN_APPLY

This flag enables the resubmit flow in Application Pending screen in Interested Creators flow.

It needs to be turned OFF, until the resubmit flow is enabled in Interested Creators flow in Support a Creator.

It affects the following pages

- `interested-creators/application-pending`
