import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const Japan: FC<SvgProps> = ({ className = "icon", ...props }) => {
  const { configuration: config } = useDependency();
  return <img className={className} alt="Japan flag" src={`${config.BASE_PATH}/img/flags/JP.png`} {...props} />;
};

export default Japan;
