.disclosure-wrapper {
  @apply flex w-full flex-col items-center justify-center bg-cover  xs:min-h-[274px] xs:bg-trust-guidelines-mobile-background xs:py-[85px]
   md:min-h-[250px] 
   md:bg-trust-guidelines-tablet-background md:py-[82px]
    lg:bg-trust-guidelines-desktop-background 2xl:bg-trust-guidelines-desktop-background;
}

.disclosure-additional-container {
  @apply w-full text-center   font-display-regular font-bold 
  text-white xs:text-mobile-display md:text-tablet-display lg:text-desktop-display;
}

.disclosure-additional-container-title {
  @apply text-center  md:px-meas0 md:pr-meas0;
}

.disclosure-additional-content-container {
  @apply flex flex-col items-center justify-center bg-navy-80 px-meas9 pb-[38px] pt-[42px];
}

.disclosure-additional-content {
  @apply m-auto w-full text-gray-10 md:max-w-[790px] lg:max-w-[792px]  2xl:max-w-[792px];
}

.disclosure-additional-content h3 {
  @apply pb-meas5 font-display-regular font-bold xs:text-mobile-h3 md:pb-meas10   md:text-tablet-h3 lg:text-desktop-h3 2xl:text-desktop-h3;
}

.disclosure-additional-content p {
  @apply pb-meas12 font-text-regular   xs:text-mobile-body-large  md:text-tablet-body-large lg:text-desktop-body-large;
}

.disclosure-parent-wrapper.disclosure-wrapper-authenticated {
  @apply pt-[104px];
}
