import { useRouter } from "next/router";
import { Icon, leftArrow } from "@eait-playerexp-cn/core-ui-kit";

const BackButton = ({ backlabel, referer }) => {
  const router = useRouter();

  const onBackClick = () => {
    if (referer) {
      router.back();
    } else {
      router.push("/dashboard");
    }
  };

  return (
    <button role="link" className="back-button-nav" onClick={onBackClick}>
      <Icon icon={leftArrow} />
      <span>{backlabel}</span>
    </button>
  );
};
export default BackButton;
