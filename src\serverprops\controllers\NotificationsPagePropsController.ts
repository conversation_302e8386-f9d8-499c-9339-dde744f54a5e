import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { NotificationsProps } from "@src/pages/notifications";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { NotificationPageLabels } from "@src/server/contentManagement/NotificationsPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class NotificationsPagePropController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<NotificationsProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<NotificationsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "notifications"
    )) as NotificationPageLabels & CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels,
        user: authenticatedUser,
        referer: req.headers?.referer || null
      }
    };
  }
}
