export default function labelsTermsAndConditions(t) {
  return {
    title: t("terms-and-conditions:title"),
    titleUpdatedTermsAndConditions: t("terms-and-conditions:titleUpdatedTermsAndConditions"),
    subTitleNewUser: t("terms-and-conditions:subTitleNewUser"),
    subTitleExistingUser: t("terms-and-conditions:subTitleExistingUser"),
    subTitleUpdatedTermsAndConditions: t("terms-and-conditions:subTitleUpdatedTermsAndConditions"),
    alreadySigned: t("terms-and-conditions:alreadySigned"),
    enterDetails: t("terms-and-conditions:enterDetails"),
    decline: t("terms-and-conditions:decline"),
    declineModalHeader: t("terms-and-conditions:declineModalHeader"),
    declineModalDescription: t("terms-and-conditions:declineModalDescription"),
    labels: {
      businessName: t("terms-and-conditions:businessName"),
      firstName: t("terms-and-conditions:firstName"),
      lastName: t("terms-and-conditions:lastName"),
      screenName: t("terms-and-conditions:screenName"),
      email: t("terms-and-conditions:email"),
      street: t("terms-and-conditions:street"),
      city: t("terms-and-conditions:city"),
      country: t("terms-and-conditions:country"),
      state: t("terms-and-conditions:state"),
      zip: t("terms-and-conditions:zip"),
      sameAddress: t("terms-and-conditions:sameAddress"),
      entityType: t("terms-and-conditions:entityType"),
      entityIndividual: t("terms-and-conditions:entityIndividual"),
      entityBusiness: t("terms-and-conditions:entityBusiness"),
      busnessNameInputDesc: t("terms-and-conditions:busnessNameInputDesc")
    },
    messages: {
      businessNameRequired: t("terms-and-conditions:businessNameRequired")
    }
  };
}
