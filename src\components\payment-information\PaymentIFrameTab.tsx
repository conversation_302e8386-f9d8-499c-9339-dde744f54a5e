import React, { FC, memo, useEffect, useMemo } from "react";
import Loading from "../loading/Loading";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { PaymentInformationPageLabels } from "@src/server/contentManagement/PaymentInformationPageMapper";
import CreatorsService from "@src/services/CreatorsService";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CreatorWithCreatorCode from "@src/server/creators/CreatorWithCreatorCode";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useRouter } from "next/router";
import { Dispatch, ErrorHandling } from "@src/utils/types";
import { LOADING } from "@src/utils";
import { AxiosError } from "axios";
import { Footer } from "@eait-playerexp-cn/onboarding-ui";
import { UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import { CreatorProfile, CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";
import { useDependency } from "@src/context/DependencyContext";

export type PaymentIFrameType = {
  pageLabels: PaymentInformationPageLabels & CommonPageLabels;
  embeddableUrl?: string;
  isLoading?: boolean;
  client: TraceableHttpClient;
  creator: CreatorWithCreatorCode | CreatorProfile;
  stableDispatch: Dispatch;
  errorHandler: ErrorHandling;
  onClose: () => void;
};

export class TipaltiEvent extends Event {
  data: {
    TipaltiIframeInfo: {
      height: number;
    };
  };
}

const PaymentIFrameTab: FC<PaymentIFrameType> = ({
  isLoading,
  embeddableUrl,
  pageLabels,
  client,
  creator,
  stableDispatch,
  errorHandler,
  onClose
}) => {
  const router = useRouter();
  const {
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE },
    creatorsClient
  } = useDependency();
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const { paymentInformationLabels, commonPageLabels } = pageLabels;

  const tipaltiHandler = (event: TipaltiEvent) => {
    const iFrame = document.querySelector(".iframe-container");
    if (event.data?.TipaltiIframeInfo?.height && iFrame) {
      iFrame.setAttribute("height", `${event.data.TipaltiIframeInfo.height}px`);
    }
  };

  useEffect(() => {
    if (window.addEventListener) {
      window.addEventListener("message", tipaltiHandler, false);
    }
    return () => window.removeEventListener("message", tipaltiHandler, false);
  }, []);

  const onNextClick = async () => {
    try {
      stableDispatch({ type: LOADING, data: true });
      const updatedAccountInformation = {
        ...creator.accountInformation,
        dateOfBirth: LocalizedDate.format(
          ((creator.accountInformation.dateOfBirth as unknown) as LocalizedDate).toDate(),
          "YYYY-MM-DD"
        )
      };
      const requestPayload = {
        accountInformation: { ...updatedAccountInformation, status: "ACTIVE" }
      };
      FLAG_PER_PROGRAM_PROFILE
        ? await creatorService.updateCreator(({
            accountInformation: { ...updatedAccountInformation },
            creatorCode: (creator as CreatorProfile)?.creatorCode?.code,
            program: { code: PROGRAM_CODE, status: "ACTIVE" }
          } as unknown) as UpdateCreatorRequest)
        : await CreatorsService.updatePrefferedValues(client, ({
            ...requestPayload,
            creatorConnectedProgram: PROGRAM_CODE
          } as unknown) as CreatorWithCreatorCode);
      stableDispatch({ type: LOADING, data: false });
      router.push("/signup-complete");
    } catch (e) {
      stableDispatch({ type: LOADING, data: false });
      errorHandler(stableDispatch, e as Error | AxiosError);
    }
  };

  const buttons = {
    next: commonPageLabels.submit,
    cancel: commonPageLabels.cancel
  };

  return isLoading ? (
    <div className="loader">
      <Loading />
    </div>
  ) : (
    <div className="onboarding-payment-settings">
      <h3 className="onboarding-payment-information-title">{commonPageLabels.paymentInfo}</h3>
      <div className="onboarding-payment-information-description">
        {paymentInformationLabels.paymentSettingsDescription}
      </div>
      {embeddableUrl && (
        <section className="onboarding-payment-iframe-cont">
          <iframe
            data-testid="iframe-container"
            className="onboarding-iframe-container"
            src={embeddableUrl}
            title="Payment IFrame"
          ></iframe>
        </section>
      )}
      <Footer onSave={onNextClick} disableSubmit={false} buttons={buttons} onCancel={onClose} />
    </div>
  );
};

export default memo(PaymentIFrameTab);
