.header-container {
  @apply flex flex-col;
}
.header-topnav-parent-container {
  @apply fixed z-[99] mt-meas20 w-full lg:h-auto;
}

.header-topnav-parent-container:has(.mobile-menu-expand) {
  @apply xs:h-full;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-child-container
  > .notifications-bell-container::after {
  @apply absolute bottom-meas0 right-[80px] h-meas2 w-meas26 bg-white opacity-0 transition-opacity duration-200;
  content: "";
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-child-container
  > .topnav-profile-avatar-container::after {
  @apply absolute bottom-meas0 right-meas8 h-meas2 w-meas26 bg-white opacity-0 transition-opacity duration-200;
  content: "";
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-active-child
  > .topnav-profile-avatar-container::after,
.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-active-child
  > .notifications-bell-container::after {
  @apply opacity-100;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > div
  > .mobile-menu-expand
  > .sidenav-mobile-container
  > .topnav-mobile-active-child {
  @apply mr-meas8 rounded-bl-none rounded-br-[4px] rounded-tl-none rounded-tr-[4px] border-l-[8px] border-solid border-l-[#429CF9] bg-custom-7;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > div
  > .mobile-menu-expand
  > .sidenav-mobile-container
  > .topnav-mobile-active-child
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container {
  @apply ml-meas2;
}
.header-container + div > .faq-page-wrapper {
  @apply pt-[6.25rem];
  transition: all 0.35s ease-in-out;
}
