import User from "@src/shared/authentication/User";

export type AccountInformationPrefferedValues = {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  nucleusId: number;
  originEmail: string;
  preferredName: string;
  preferredPronouns: string;
};

export default class AccountInformationPrefferedValuesInput {
  readonly firstName: string;
  readonly lastName: string;
  readonly dateOfBirth: string;
  readonly nucleusId: number;
  readonly originEmail: string;
  readonly defaultGamerTag: string;
  readonly preferredName: string;
  readonly preferredPronouns: string;

  constructor(creator: User, values: AccountInformationPrefferedValues) {
    this.firstName = values.firstName;
    this.lastName = values.lastName;
    this.dateOfBirth = values.dateOfBirth;
    this.nucleusId = values.nucleusId;
    this.originEmail = values.originEmail;
    this.defaultGamerTag = creator.username;
    this.preferredName = values.preferredName;
    this.preferredPronouns = values.preferredPronouns;
  }
}
