import { render, screen } from "@testing-library/react";
import FaqPageComponent from "./FaqPageComponent";
import faqPageComponentLabels from "__tests__/translations/faq";
import { axe } from "jest-axe";

describe("FaqsPage", () => {
  it("renders the FAQ page labels correctly", () => {
    render(<FaqPageComponent labels={faqPageComponentLabels} />);

    expect(screen.getByText(faqPageComponentLabels.pageTitle)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.pageTitle)).toHaveClass("faq-page-heading");
    expect(screen.getByText(faqPageComponentLabels.pageSubtitle)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.pageSubtitle)).toHaveClass("faq-page-subHeading");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsTitle)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsTitle)).toHaveClass("faq-page-content-title");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion1)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion1)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer1)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer1)).toHaveClass("faq-page-answer");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion2)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion2)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer2)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer2)).toHaveClass("faq-page-answer");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion3)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion3)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer3)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer3)).toHaveClass("faq-page-answer");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion4)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsQuestion4)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer4)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.generalFAQsAnswer4)).toHaveClass("faq-page-answer");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsTitle)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsTitle)).toHaveClass("faq-page-content-title");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsQuestion1)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsQuestion1)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsAnswer1)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsAnswer1)).toHaveClass("faq-page-answer");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsQuestion2)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsQuestion2)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsAnswer2)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsAnswer2)).toHaveClass("faq-page-answer");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsQuestion3)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsQuestion3)).toHaveClass("faq-page-question");
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsAnswer3)).toBeInTheDocument();
    expect(screen.getByText(faqPageComponentLabels.eligibilityFAQsAnswer3)).toHaveClass("faq-page-answer");
  });

  it("is accessible", async () => {
    const { container } = render(<FaqPageComponent labels={faqPageComponentLabels} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
