import Link from "next/link";

type TopNavBarProps = {
  locale: string;
  labels: {
    topNavigation: string;
  };
};

const TopNavBar = ({ locale, labels }: TopNavBarProps): JSX.Element => {
  return (
    <nav className="top-nav-bar" id="top-nav-bar" data-testid="top-nav-bar" aria-label={labels.topNavigation}>
      <Link href={`https://www.ea.com/${locale}`} className="top-nav-bar-link" target="_blank">
        <h1 className="sr-only">Visit the Electronic Arts Homepage</h1>
        <img src={"/support-a-creator/img/logo-ea-wordmark--light.svg"} alt="" />
      </Link>
    </nav>
  );
};

export default TopNavBar;
