import React, { memo, useCallback } from "react";
import {
  AccountCard,
  facebookIcon,
  instagramIcon,
  tiktokIcon,
  twitchIcon,
  youTubeIcon
} from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import {
  GET_FB_PAGES,
  POPUP_OPENED,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS,
  SHOW_FACEBOOK_PAGES,
  WINDOW_PARAMS
} from "../../utils";
import ConnectedAccountsService from "../../../src/services/ConnectedAccountsService";
import { toast } from "react-toastify";
import { useDependency } from "@src/context/DependencyContext";

const AddAccounts = memo(function AddAccounts({
  imageType,
  accountType,
  url,
  setShowAddConfirmation,
  showAddConfirmation,
  accountId,
  disconnected,
  isExpired,
  labels,
  isInterestedCreator
}) {
  const { errorHandler } = useDependency();
  const { dispatch } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const onAddAccount = useCallback(() => {
    if (!showAddConfirmation) {
      setShowAddConfirmation(true);
      const loginWindow = window.open(url, "_blank", WINDOW_PARAMS);
      isInterestedCreator && toast.dismiss();
      stableDispatch({ type: POPUP_OPENED, data: true });
      const loop = setInterval(function () {
        if (loginWindow.closed) {
          clearInterval(loop);
          stableDispatch({ type: POPUP_OPENED, data: false });
          setShowAddConfirmation(false);
          ConnectedAccountsService.clearAccountType()
            .then(() => {
              isInterestedCreator && accountType === "FACEBOOK" && dispatch({ type: GET_FB_PAGES, data: true });
              isInterestedCreator && accountType === "FACEBOOK" && dispatch({ type: SHOW_FACEBOOK_PAGES, data: true });
              isInterestedCreator
                ? dispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true })
                : location.reload(); // refresh to trigger getServersideProps rerun and spew new session props
            })
            .catch((e) => errorHandler(stableDispatch, e));
        }
      }, 100);
    }
  }, [showAddConfirmation, url, setShowAddConfirmation, stableDispatch]);

  return (
    <>
      <AccountCard
        {...{
          ...{
            accountIcon: imageType,
            noAccount: true,
            expired: !disconnected && isExpired,
            handleAddAccount: onAddAccount,
            accountId,
            accountType,
            labels
          }
        }}
      />
    </>
  );
});

const ConnectAccountsInputs = ({ labels, setShowAddConfirmation, showAddConfirmation, isInterestedCreator }) => {
  const { configuration } = useDependency();
  const accounts = [
    {
      label: "youTube",
      accountIcon: youTubeIcon,
      accountType: "YOUTUBE",
      url: `${configuration.BASE_PATH}/api/youtube-login`
    },
    {
      label: "twitch",
      accountIcon: twitchIcon,
      accountType: "TWITCH",
      url: `${configuration.BASE_PATH}/api/twitch-login`
    },
    {
      label: "facebook",
      accountIcon: facebookIcon,
      accountType: "FACEBOOK",
      url: `${configuration.BASE_PATH}/api/facebook-login`
    },
    {
      label: "instagram",
      accountIcon: instagramIcon,
      accountType: "INSTAGRAM",
      url: `${configuration.BASE_PATH}/api/instagram-login`
    },
    {
      label: "tiktok",
      accountIcon: tiktokIcon,
      accountType: "TIKTOK",
      url: `${configuration.BASE_PATH}/api/tiktok-login`
    }
  ];
  return (
    <div>
      <div className="mg-connected-accounts-container">
        <h5 className="mg-connected-accounts-title">{labels.subTitle}</h5>
        <div className="connect-account-card-container">
          {accounts.map(({ url, accountType, accountIcon }, ind) => (
            <AddAccounts
              key={`accounts-${ind}`}
              {...{ url, setShowAddConfirmation, showAddConfirmation, accountType, accountIcon, labels }}
              accountType={accountType}
              imageType={accountIcon}
              isInterestedCreator={isInterestedCreator}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
export default ConnectAccountsInputs;
