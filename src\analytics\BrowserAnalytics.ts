import { Identity } from "@eait-playerexp-cn/identity-types";
import { CreatorWithPayableStatusProfile } from "@src/services/CreatorsService";
import User from "@src/shared/authentication/User";
import { ampli, Ampli } from "../../analytics/browser/src/ampli";

type CreatorIdentity = {
  locale: string;
};

type CreatorPlatforms = CreatorIdentity & {
  primaryPlatform: string;
  secondaryPlatforms: string[];
};

type CommunicationPreferences = CreatorIdentity & {
  contentLanguages: string;
};

type FooterLink = CreatorIdentity & { url: string };

type ContinuedApplication = CreatorIdentity & { creatorTypes?: string[]; page: string; finalStep: boolean };

type ConnectedNewSocialAccount = CreatorIdentity & {
  accountType: string;
  deliverableTitle?: string;
  deliverableType?: string;
};

type CheckedApplicationStatus = CreatorIdentity & {
  status: "Accepted" | "Rejected" | "Pending";
};

type CreatorApplication = CreatorIdentity & {
  page: string;
};

type CreatorProfileUpdate = CreatorIdentity & {
  creator: CreatorWithPayableStatusProfile;
};

type CreatorTypesUpdate = CreatorProfileUpdate & {
  selectedCreatorTypes?: string[];
};

type WithLabel = { label: string };

type CreatorPlatformsUpdate = CreatorProfileUpdate & {
  selectedPlatforms?: WithLabel[];
};

type CanceledOnboarding = CreatorIdentity & {
  page: string;
};

type TermsAndConditions = CreatorIdentity & {
  accepted: boolean;
};

type AppliedDateRangeFilter = CreatorIdentity & {
  selectedDateRange: string;
};

type AppliedPaymentStatusFilter = CreatorIdentity & {
  selectedPaymentStatus: string;
};

type AppliedPaymentTypeFilter = CreatorIdentity & {
  selectedPaymentType: string;
};

type AppliedAllPaymentFilters = AppliedDateRangeFilter & AppliedPaymentStatusFilter & AppliedPaymentTypeFilter;

type RemovedPaymentFilter = CreatorIdentity & {
  removedFilteredValue: string;
  removedFilterType: string;
};

export type CreatorType = "CREATOR" | "INTERESTED_CREATOR";

export type AuthenticatedUser = {
  analyticsId: string | undefined;
  needsMigration?: boolean;
  username: string;
  status?: string;
  avatar?: string;
  tier?: string;
  isPayable?: boolean;
  type?: CreatorType;
  isFlagged?: boolean;
  programs?: string[];
  creatorCode?: string;
};

export type InitialInterestedCreator = {
  analyticsId: string | undefined;
  nucleusId: number | string;
  defaultGamerTag: string;
  originEmail: string;
  dateOfBirth: string;
  username?: string;
};

export class AuthenticatedUserFactory {
  static fromIdentity(identity: Identity, defaultAvatar: string, program: string): AuthenticatedUser {
    return {
      analyticsId: identity.analyticsId(),
      needsMigration: identity.needsMigration,
      username: identity.username,
      status: identity.statusFor(program),
      avatar: identity.avatar || defaultAvatar,
      tier: identity.tier ?? null,
      isPayable: identity.payable ?? false,
      isFlagged: identity.flagged ?? false,
      programs: identity.programs.map((program) => program.code) || [],
      creatorCode: identity.creatorCode || null,
      type: identity.type
    };
  }

  /**
   * @deprecated
   */
  static fromSession(user: User, FLAG_CREATORS_API_WITH_PROGRAM?: boolean): AuthenticatedUser {
    const authenticatedUser: AuthenticatedUser = {
      analyticsId: user.analyticsId,
      needsMigration: user.needsMigration,
      username: user.username,
      status: user.status,
      avatar: user.avatar,
      tier: user?.tier ?? null,
      isPayable: user?.isPayable ?? false,
      isFlagged: user?.isFlagged ?? null,
      programs: user?.programs || [],
      creatorCode: user?.creatorCode || "",
      type: "CREATOR"
    };

    if (FLAG_CREATORS_API_WITH_PROGRAM) authenticatedUser.programs = user.programs;

    return authenticatedUser;
  }

  static fromInterestedCreator(interestedCreator: InitialInterestedCreator | Identity): AuthenticatedUser {
    const isIdentity = interestedCreator instanceof Identity;
    return {
      analyticsId: isIdentity
        ? (interestedCreator as Identity).analyticsId()
        : (interestedCreator as InitialInterestedCreator).analyticsId || null,
      username: isIdentity
        ? (interestedCreator as Identity).username
        : (interestedCreator as InitialInterestedCreator).defaultGamerTag || null,
      type: "INTERESTED_CREATOR"
    };
  }
}

type MarketingPage = CreatorIdentity & {
  page: string;
};

type BrowserAnalyticsOptions = {
  user?: AuthenticatedUser;
  program: string;
};

class BrowserAnalytics {
  private readonly client: Ampli;
  private readonly user?: AuthenticatedUser;
  private readonly program?: string;

  constructor(options?: BrowserAnalyticsOptions, client?: Ampli) {
    this.user = options.user;
    this.client = client || ampli;
    this.program = options.program;
  }

  startedOnboardingFlow(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.startedOnboardingFlow({
      Program: this.program
    });
  }

  canceledOnboardingFlow(canceledOnboarding: CanceledOnboarding): void {
    this.client.identify(this.user?.analyticsId, { Locale: canceledOnboarding.locale });
    this.client.canceledOnboardingFlow({
      "Page Abandoned": canceledOnboarding.page,
      Program: this.program
    });
  }

  confirmedPlatform(creatorPlatforms: CreatorPlatforms): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorPlatforms.locale,
      "Primary Platform": creatorPlatforms.primaryPlatform,
      "Secondary Platform": creatorPlatforms.secondaryPlatforms
    });
    this.client.confirmedPlatform({
      "Primary Platform": creatorPlatforms.primaryPlatform,
      "Secondary Platform": creatorPlatforms.secondaryPlatforms,
      Program: this.program
    });
  }

  confirmedCommunicationPreferences(communicationPreferences: CommunicationPreferences): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: communicationPreferences.locale,
      "Content Languages": communicationPreferences.contentLanguages
    });
    this.client.confirmedCommunicationPreferences({
      Program: this.program
    });
  }

  signedTermsAndConditions(termsAndConditions: TermsAndConditions): void {
    this.client.identify(this.user?.analyticsId, { Locale: termsAndConditions.locale });
    this.client.signedTermsAndConditions({ "Agreed to T&C's": termsAndConditions.accepted, Program: this.program });
  }

  completedOnboardingFlow(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.completedOnboardingFlow({ Program: this.program });
  }

  updatedCreatorTypesInProfile(profileUpdate: CreatorTypesUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Creator Types": profileUpdate.selectedCreatorTypes
    });
    this.client.updatedCreatorTypesInProfile({
      "Added Creator Types": profileUpdate.creator.addedCreatorTypes(profileUpdate.selectedCreatorTypes || []),
      "Removed Creator Types": profileUpdate.creator.removedCreatorTypes(profileUpdate.selectedCreatorTypes || []),
      Program: this.program
    });
  }

  updatedPrimaryFranchise(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Primary Franchise": profileUpdate.creator.preferredPrimaryFranchiseLabel()
    });
    this.client.updatedPrimaryFranchise({
      "Selected Franchise(s)": [profileUpdate.creator.preferredPrimaryFranchiseLabel()],
      Program: this.program
    });
  }

  updatedSecondaryFranchises(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Secondary Franchise": profileUpdate.creator.preferredSecondaryFranchisesLabels()
    });
    this.client.updatedSecondaryFranchises({
      "Selected Franchise(s)": profileUpdate.creator.preferredSecondaryFranchisesLabels(),
      Program: this.program
    });
  }

  updatedPrimaryPlatformInProfile(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Primary Platform": profileUpdate.creator.preferredPrimaryPlatformLabel()
    });
    this.client.updatedPrimaryPlatformInProfile({
      "Selected Platform(s)": [profileUpdate.creator.preferredPrimaryPlatformLabel()],
      Program: this.program
    });
  }

  updatedSecondaryPlatformsInProfile(profileUpdate: CreatorPlatformsUpdate): void {
    const platforms = profileUpdate.selectedPlatforms.map((platform) => platform.label);
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Secondary Platform": platforms
    });
    this.client.updatedSecondaryPlatformsInProfile({
      "Removed Platforms": profileUpdate.creator.removedSecondaryPlatforms(platforms),
      "Selected Platform(s)": profileUpdate.creator.addedSecondaryPlatforms(platforms),
      Program: this.program
    });
  }

  updatedBasicInformation(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: identity.locale
    });
    this.client.updatedBasicInformation({ Program: this.program });
  }

  viewedMarketingPage(marketingPage: MarketingPage): void {
    this.client.identify(this.user?.analyticsId, { Locale: marketingPage.locale });
    this.client.viewedMarketingPage({ "Page Displayed": marketingPage.page, Program: this.program });
  }

  clickedFooterLink(footerLink: FooterLink): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: footerLink.locale
    });
    this.client.clickedFooterLink({
      "Link Clicked": footerLink.url,
      Program: this.program
    });
  }

  signedOutOfCreatorNetwork(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: identity.locale
    });
    this.client.signedOutOfCreatorNetwork({ Program: this.program });
  }

  visitedMyProfile(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: identity.locale
    });
    this.client.visitedMyProfile({ Program: this.program });
  }

  clickedEmailPocLink(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.clickedEmailPocLink({ Program: this.program });
  }

  emailSentToPoc(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.emailSentToPoc({ Program: this.program });
  }

  checkedApplicationStatus(applicationStatus: CheckedApplicationStatus): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: applicationStatus.locale
    });
    this.client.checkedApplicationStatus({ "Application Status": applicationStatus.status, Program: this.program });
  }

  startedCreatorApplication(creatorApplication: CreatorApplication): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorApplication.locale,
      Type: this.user?.type,
      Status: this.user?.status
    });
    this.client.startedCreatorApplication({ "Application Page": creatorApplication.page, Program: this.program });
  }

  continuedCreatorApplication(continuedApplication: ContinuedApplication): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: continuedApplication.locale,
      "Creator Types": continuedApplication.creatorTypes,
      Type: this.user?.type,
      Status: this.user?.status
    });
    this.client.continuedCreatorApplication({
      "Application Page": continuedApplication.page,
      "Final Step": continuedApplication.finalStep,
      Program: this.program
    });
  }

  cancelledCreatorApplication(creatorApplication: CreatorApplication): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorApplication.locale,
      Type: this.user?.type,
      Status: this.user?.status
    });
    this.client.cancelledCreatorApplication({ "Application Page": creatorApplication.page, Program: this.program });
  }

  connectedNewSocialAccount(connectedNewSocialAccount: ConnectedNewSocialAccount): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: connectedNewSocialAccount.locale
    });
    this.client.connectedNewSocialAccount({
      "Social Channel Type": connectedNewSocialAccount.accountType,
      "Deliverable Title": connectedNewSocialAccount.deliverableTitle,
      "Deliverable Type": connectedNewSocialAccount.deliverableType,
      Program: this.program
    });
  }

  /* Creator Wallet Events */
  clickedPaymentInformationInMyProfile(clickedPaymentInformationInMyProfile: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentInformationInMyProfile.locale
    });
    this.client.clickedPaymentInformationInMyProfile({ Program: this.program });
  }

  clickedPaidOpportunitiesWhenThereIsNoTransaction(
    clickedPaidOpportunitiesWhenThereIsNoTransaction: CreatorIdentity
  ): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaidOpportunitiesWhenThereIsNoTransaction.locale
    });
    this.client.clickedPaidOpportunitiesWhenThereIsNoTransaction({ Program: this.program });
  }

  clickedPaymentDetailsIncompleteTooltip(clickedPaymentDetailsIncompleteTooltip: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentDetailsIncompleteTooltip.locale
    });
    this.client.clickedPaymentDetailsIncompleteTooltip({ Program: this.program });
  }

  clickedPaymentDetailsIncompleteHelperBanner(clickedPaymentDetailsIncompleteHelperBanner: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentDetailsIncompleteHelperBanner.locale
    });
    this.client.clickedPaymentDetailsIncompleteHelperBanner({ Program: this.program });
  }

  clickedPaymentSettingsTab(clickedPaymentSettingsTab: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentSettingsTab.locale
    });
    this.client.clickedPaymentSettingsTab({ Program: this.program });
  }

  clickedOpportunityDescription(clickedOpportunityDescription: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedOpportunityDescription.locale
    });
    this.client.clickedOpportunityDescription({ Program: this.program });
  }

  downloadedPaymentContract(downloadedPaymentContract: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: downloadedPaymentContract.locale
    });
    this.client.downloadedPaymentContract({ Program: this.program });
  }

  openedPaymentsFiltersForm(openedPaymentsFiltersForm: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: openedPaymentsFiltersForm.locale
    });
    this.client.openedPaymentsFiltersForm({ Program: this.program });
  }

  appliedDateRangeFilter(appliedDateRangeFilter: AppliedDateRangeFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedDateRangeFilter.locale
    });
    this.client.appliedDateRangeFilter({
      "Selected Date Range Option": appliedDateRangeFilter.selectedDateRange,
      Program: this.program
    });
  }

  appliedPaymentStatusFilter(appliedPaymentStatusFilter: AppliedPaymentStatusFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedPaymentStatusFilter.locale
    });
    this.client.appliedPaymentStatusFilter({
      "Payment Status Selected": appliedPaymentStatusFilter.selectedPaymentStatus,
      Program: this.program
    });
  }

  appliedPaymentTypeFilter(appliedPaymentTypeFilter: AppliedPaymentTypeFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedPaymentTypeFilter.locale
    });
    this.client.appliedPaymentTypeFilter({
      "Payment Type Selected": appliedPaymentTypeFilter.selectedPaymentType,
      Program: this.program
    });
  }

  appliedAllPaymentFilters(appliedAllPaymentFilters: AppliedAllPaymentFilters): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedAllPaymentFilters.locale
    });
    this.client.appliedAllPaymentFilters({
      "Payment Status Selected": appliedAllPaymentFilters.selectedPaymentStatus,
      "Payment Type Selected": appliedAllPaymentFilters.selectedPaymentType,
      "Selected Date Range Option": appliedAllPaymentFilters.selectedDateRange,
      Program: this.program
    });
  }

  removedPaymentFilter(removedPaymentFilter: RemovedPaymentFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: removedPaymentFilter.locale
    });

    const FILTER_TYPE_AMPLI_MAPPING = {
      range: "Date Range",
      status: "Payment Status",
      opportunityType: "Opportunity Type"
    };

    this.client.removedPaymentFilter({
      "Removed Filtered Value": removedPaymentFilter.removedFilteredValue,
      "Removed Filter Type": FILTER_TYPE_AMPLI_MAPPING[removedPaymentFilter.removedFilterType],
      Program: this.program
    });
  }
}

export default BrowserAnalytics;
