.payment-information-container {
  @apply min-h-screen pl-meas8 pr-meas8 pt-meas22 font-text-regular text-white xs:mt-meas12 md:pl-[65px] md:pr-[65px] lg:mt-meas24 lg:pl-[118px] lg:pr-[118px];
}
.payment-info {
  @apply mt-meas2 font-text-regular text-gray-10;
}

.payment-info > .profile-payment-information-intro-title {
  @apply mb-meas8;
}

.payment-info .cn-tabs-header {
  @apply flex justify-between xl:w-[416px];
}

.payment-info .cn-tabs-header > button {
  @apply pb-meas4;
}

.payment-info .cn-tabs-content {
  @apply mb-meas8;
}

.payment-info .cn-tabs-content .payment-settings > div {
  @apply border-b border-t border-white border-opacity-30 py-meas24;
}

.payment-info .loader {
  @apply border-t border-white border-opacity-30 py-meas24;
}

.payment-info .cn-tabs-content {
  @apply mt-meas0;
}

.payment-info .cn-tabs-content .payment-settings .payment-iframe-cont {
  @apply mt-meas8;
}

.payment-info .payment-iframe-cont > iframe {
  @apply min-h-[440px] w-full min-w-[288px] md:min-h-[716px] md:min-w-[640px] lg:min-h-[796px] lg:min-w-[780px];
}

.payment-info-header {
  @apply flex flex-col flex-wrap items-start justify-start md:flex-row md:items-center;
}

.payment-info-title {
  @apply flex-none pb-[5px] leading-7 text-white md:text-tablet-h4 md:leading-7;
}

.payment-info-payable-details {
  @apply ml-meas0 md:ml-meas7;
}

.payment-info.creator-wallet .tooltip::after {
  @apply w-[210px] rounded-[3px] p-[10px] leading-4 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1 xl:w-[275px];
  background-image: none;
}

.payment-info.creator-wallet .tooltip.top::after {
  transform: translate(calc(-50% + 36px), calc(-100% - 10px));
}

.payment-info-payable-details .pill-btn-wrapper {
  @apply mb-meas12 mt-meas0 md:mb-meas0;
}

.payment-info-payable-details .pill-btn-warning {
  @apply cursor-default;
}

.payment-info.creator-wallet .cn-tabs-container {
  @apply xs:mb-meas0 xs:pb-[47px] md:mb-[47px] md:mb-meas30 md:pb-meas0 xl:mb-meas33;
}

.payment-info.creator-wallet .cn-tabs-content {
  @apply mb-meas0;
}

.payment-info.creator-wallet .cn-tabs-header {
  @apply mr-meas9 flex justify-start;
}

.payment-info.creator-wallet .cn-tabs-header > .cn-tab {
  @apply mr-meas9 font-text-bold text-white md:mr-meas18 xl:mr-meas6;
}

.payment-info.creator-wallet .cn-tabs-header > .cn-tab.active {
  @apply relative border-0;
}

.payment-info.creator-wallet .cn-tabs-header > .cn-tab.active:after {
  @apply absolute bottom-[0.5px] left-meas0 w-full border-b-[3px] text-indigo-40 content-[""] xl:border-b-[4px];
}

.payment-info.creator-wallet .payment-info-header {
  @apply md:mb-meas12 xl:mb-meas18;
}

.payment-info.creator-wallet .payment-info-title {
  @apply md:pb-meas0 xl:text-[32px] xl:leading-[38px] xl:tracking-[1px];
}

.payment-info.creator-wallet .banner-content-heading {
  @apply font-display-bold leading-[18px] tracking-[1px];
}
.payment-info.creator-wallet .pagination-container {
  @apply my-meas12 font-text-bold;
}
.payment-info.creator-wallet .pagination-text svg {
  @apply relative top-[-1px] mx-[10px] h-meas10 w-meas6;
  color: rgba(115, 115, 115, 1);
}
.payment-info.creator-wallet .content-grid-last-record {
  @apply border-b-0;
}
.payment-details-transaction-history-wrapper.with-pagination .pagination-container {
  @apply mb-[22px] mt-[44px];
}

.payment-info-tooltip-content-click-here {
  @apply mx-meas2 cursor-pointer underline;
}
