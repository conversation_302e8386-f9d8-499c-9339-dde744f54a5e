import React, { memo, useCallback } from "react";
import { useRouter } from "next/router";
import { setCookie } from "../../utils";
import { eaALogoAffiliate, Footer } from "@eait-playerexp-cn/core-ui-kit";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";

export type FooterPageLabels = {
  commonPageLabels: {
    how: string;
    reward: string;
    perks: string;
    faq: string;
    policies: string;
    legal: string;
    disclaimer: string;
    updates: string;
    terms: string;
    privacy: string;
    rights: string;
    report: string;
    faqs: string;
    disclosure: string;
    policy: string;
  };
};

export type FooterProps = {
  locale: string;
  labels: FooterPageLabels;
};

export default memo(function ProgramFooter({ locale, labels }: FooterProps) {
  const { commonPageLabels } = labels;

  const footerLabels = {
    how: commonPageLabels.how,
    reward: commonPageLabels.reward,
    perks: commonPageLabels.perks,
    faq: commonPageLabels.faq,
    policies: commonPageLabels.policies,
    legal: commonPageLabels.legal,
    disclaimer: commonPageLabels.disclaimer,
    updates: commonPageLabels.updates,
    terms: commonPageLabels.terms,
    privacy: commonPageLabels.privacy,
    rights: commonPageLabels.rights,
    report: commonPageLabels.report,
    faqs: commonPageLabels.faqs,
    disclosure: commonPageLabels.disclosure,
    policy: commonPageLabels.policy
  };

  const router = useRouter();
  const { configuration: config, analytics } = useDependency();
  const programCode = config.PROGRAM_CODE;
  const navigateToMarketingPage = (url: string) => {
    analytics?.clickedFooterLink({ locale, url });
    router.push(url);
  };
  const howItWorksLink = { label: footerLabels.how, onClick: () => navigateToMarketingPage("/#how-it-works") };
  const faqsLink = { label: footerLabels.faqs, onClick: () => navigateToMarketingPage("/faq") };
  const policyLink = {
    label: footerLabels.policy,
    onClick: () => navigateToMarketingPage("/trust-and-safety-guidelines")
  };
  const disclosureLink = { label: footerLabels.disclosure, onClick: () => navigateToMarketingPage("/disclosure") };
  const legalLink = { label: footerLabels.legal, href: "https://www.ea.com/legal-notices" };
  const disclaimerLink = { label: footerLabels.disclaimer, href: "https://www.ea.com/legal/legal-disclosures" };
  const updatesLink = { label: footerLabels.updates, href: "http://www.ea.com/1/service-updates" };
  const termsLink = { label: footerLabels.terms, href: "http://tos.ea.com/legalapp/WEBTERMS/US/en/PC/" };
  const privacyLink = { label: footerLabels.privacy, href: "https://www.ea.com/privacy-policy" };
  const reportLink = {
    label: footerLabels.report,
    href: `https://help.ea.com/${locale}/help-contact-us/?product=origin&topic=report-toxicity&category=report-concerns-or-harassment&subCategory=report-player`
  };
  const footerLinksWeb = {
    internal: [howItWorksLink, faqsLink, policyLink, disclosureLink],
    external: {
      sections: [
        [legalLink, disclaimerLink, updatesLink],
        [termsLink, privacyLink, reportLink]
      ]
    }
  };

  const footerLinksMobile = {
    internal: [howItWorksLink, faqsLink, policyLink, disclosureLink],
    external: {
      sections: [[legalLink, disclaimerLink], [updatesLink], [termsLink], [privacyLink], [reportLink]]
    }
  };

  const updateLocale = useCallback(
    (selectedItem) => {
      const { locale = undefined } = selectedItem;
      const { pathname, asPath, query } = router;
      if (locale) {
        setCookie(locale);
        localStorage.setItem("locale", JSON.stringify(selectedItem));
        router.push({ pathname, query }, asPath, { locale });
      }
    },
    [router]
  );

  return (
    <Footer
      countriesDropdown={{
        countries: [],
        onChange: updateLocale,
        defaultLocale: locale
      }}
      programIcon={eaALogoAffiliate}
      footerLinksWeb={footerLinksWeb}
      footerLinksMobile={footerLinksMobile}
      labels={{
        rights: footerLabels.rights,
        year: `${LocalizedDate.year()}`
      }}
      programCode={programCode}
    />
  );
});
