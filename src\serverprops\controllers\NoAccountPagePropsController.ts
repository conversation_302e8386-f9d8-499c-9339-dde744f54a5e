import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { Identity } from "@eait-playerexp-cn/identity-types";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { NoAccount } from "@src/pages/interested-creators/no-account";
import { NoAccountPageLabels } from "@src/server/contentManagement/NoAccountPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import featureFlags from "@src/utils/feature-flags";
import { GetServerSidePropsResult } from "next";

export default class NoAccountPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<NoAccount> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<NoAccount>> {
    const interestedCreator = this.hasSession(req, "noAccountCreator")
      ? (this.session(req, "noAccountCreator") as Identity)
      : null;

    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };

    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : null;

    const pageLabels = (await this.contents.getPageLabels(this.currentLocale, "noAccount")) as NoAccountPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        pageLabels,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator),
        locale: this.currentLocale,
        showInitialMessage
      }
    };
  }
}
