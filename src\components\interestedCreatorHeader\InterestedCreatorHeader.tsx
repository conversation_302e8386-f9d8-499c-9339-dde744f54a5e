import React, { FC, memo } from "react";
import { close, Icon } from "@eait-playerexp-cn/core-ui-kit";
import Link from "next/link";
import SvgEaLogo from "@components/icons/EaLogo";

type InterestedCreatorHeaderProps = {
  logoLabel: string;
  closeButtonAriaLabel: string;
  onClose?: () => void;
};

const InterestedCreatorHeader: FC<InterestedCreatorHeaderProps> = memo(function Header({
  logoLabel,
  onClose,
  closeButtonAriaLabel
}: InterestedCreatorHeaderProps) {
  return (
    <div className="interested-creator-header-container">
      <div className="interested-creator-header">
        <div className="interested-creator-header-logo">
          <Link href="/">
            <Icon icon={SvgEaLogo} width="3rem" height="3rem" />
            <span className="interested-creator-header-logo-text">{logoLabel}</span>
          </Link>
        </div>
        {onClose && (
          <button aria-label={closeButtonAriaLabel} className="interested-creator-header-close" onClick={onClose}>
            <Icon icon={close} id="interested-creator-header-close-icon" />
          </button>
        )}
      </div>
    </div>
  );
});

export default memo(InterestedCreatorHeader);
