.dashboard-filter-wrapper {
  @apply flex flex-row items-center justify-between;
}
.dashboard-filter-pills-status {
  @apply flex flex-row items-center;
}
.dashboard-filter-pills-text {
  @apply hidden font-text-regular text-desktop-body-small font-normal text-white md:block;
}
.dashboard-filter-pills-text-active {
  @apply block font-text-regular text-desktop-body-small font-normal text-white;
}
.dashboard-filter-pills-date {
  @apply hidden font-text-regular text-desktop-body-small  font-normal text-gray-30 md:block;
  font-variant-ligatures: none;
}
.dashboard-filter-pills-date-active {
  @apply block font-text-regular text-desktop-body-small font-normal text-gray-30;
  font-variant-ligatures: none;
}
.dashboard-filter-pills-status .filter-pill-wrapper {
  @apply mx-meas0 md:mx-meas5;
}

.bar-chart-filter-section-landscape-view .filter-pill-wrapper {
  @apply mx-meas5;
}

.dashboard-filter-pills-text-not-active {
  @apply mr-meas5;
}
.dashboard-filter-button {
  @apply flex-auto;
}
