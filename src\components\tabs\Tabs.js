import React, { createContext, memo, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { useDetectScreen } from "../../utils";
import ConditionalWrapper from "../ConditionalWrapper";

const TabsContext = createContext(0);

export function useTabContext() {
  return useContext(TabsContext) || {};
}

const Tab = memo(function Tab({ id, title, disabled = false, onTabClick, tabsToDropDown }) {
  const { activeTab, setActiveTab } = useTabContext({ activeTab: null, setActiveTab: null });
  const isMobileOrTabBreakpoint = useDetectScreen(1279) && tabsToDropDown; // true means mobile view
  const onClick = useCallback(() => {
    setActiveTab(id);
    onTabClick?.(id);
  }, [id, onTabClick]);
  const tabProps = useMemo(
    () => ({
      className: `${(activeTab === id && "active") || "inactive"} cn-tab`,
      onClick,
      children: title,
      disabled,
      value: id,
      selected: activeTab === id
    }),
    [activeTab, id, onClick, disabled, title]
  );

  return (isMobileOrTabBreakpoint && <option {...tabProps} />) || <button {...tabProps} />;
});

const TabContent = memo(function TabContent() {
  const { tabs, activeTab = 0 } = useTabContext({ activeTab: null, tabs: [] });
  const activeTabContent = tabs.filter((t) => t.id === activeTab);
  const content = activeTabContent.length && activeTabContent[0].content;
  return <main className="cn-tabs-content">{content}</main>;
});

const Tabs = memo(function Tabs({
  tabs = [],
  mode = "vertical",
  onTabClick = null,
  activeTabId = (tabs[0] && tabs[0].id) || null,
  tabsToDropDown = false, // this prop will not convert tabs to dropdown by default
  tabId = "tab-id"
}) {
  // mode vertical | horizontal
  const [activeTab, setActiveTab] = useState(activeTabId);
  const isMobileOrTabBreakpoint = useDetectScreen(1279) && tabsToDropDown; // true means mobile view
  const onChange = useCallback(({ target: { value } }) => {
    setActiveTab(value);
    onTabClick?.(value);
  }, []);

  useEffect(() => {
    setActiveTab(activeTabId);
  }, [activeTabId]);

  const wrapper = useCallback(
    (children) => (
      <select id={tabId} {...{ onChange }}>
        {children}{" "}
      </select>
    ),
    [onChange]
  );
  return (
    <TabsContext.Provider value={{ tabs, activeTab, setActiveTab }}>
      <section className={`cn-tabs-container ${mode} ${(isMobileOrTabBreakpoint && "mobileTabView") || ""}`}>
        <header className="cn-tabs-header">
          <ConditionalWrapper {...{ condition: isMobileOrTabBreakpoint, wrapper }}>
            {tabs.map((tab, ind) => {
              return <Tab key={`tab-${ind}`} {...tab} {...{ setActiveTab, activeTab, onTabClick, tabsToDropDown }} />;
            })}
          </ConditionalWrapper>
        </header>
        <TabContent />
      </section>
    </TabsContext.Provider>
  );
});

export default Tabs;
