.dashboard-container {
  @apply flex flex-col;
}
.dashboard-creator-code-card {
  @apply mx-meas6 mb-meas7 mt-meas6 md:mx-meas10 md:mb-meas9 md:mt-meas12 lg:mx-[30px] lg:mb-meas12 lg:mt-[30px];
}
.dashboard-filter {
  @apply mx-meas6 mb-meas7 md:mx-meas10 md:mb-meas9 md:mt-meas0 lg:mx-[30px] lg:mb-meas12;
}
.dashboard-filter-mobile {
  @apply mb-meas12 mt-meas7;
}
.dashboard-charts-container {
  @apply flex w-full flex-col lg:flex-row;
}
.dashboard-charts-statistic-card {
  @apply mx-meas6 overflow-x-auto md:ml-meas10 md:mr-meas4 lg:ml-[30px] lg:mr-meas0 lg:box-border lg:w-[324px] lg:flex-none;
}
.dashboard-charts-bar-chart {
  @apply mx-meas6 mt-meas7 md:mx-meas10 md:mt-meas12 lg:ml-meas13 lg:mr-[30px] lg:mt-meas0 lg:box-border lg:flex-grow;
}
.dashboard-charts-bar-chart .bar-chart-container-portrait-view {
  @apply lg:!h-full;
}
.dashboard-earnings-disclaimer {
  @apply mx-meas7 mt-meas7 pb-meas7 font-text-regular text-[10px] font-normal not-italic leading-normal text-[#D9D9D9] md:mx-meas10 md:mt-meas12 md:pb-meas10 md:text-end md:text-[12px] lg:mx-meas16;
}
.dashboard-loader {
  @apply min-h-full bg-alpha-white-6;
}
.dashboard-charts-empty-card {
  @apply w-full p-meas6 pb-meas19 pt-meas6 md:px-meas11 md:pb-meas30 md:pt-meas7 lg:px-meas15 lg:pb-meas31 lg:pt-meas32;
}
