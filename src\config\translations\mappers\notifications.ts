import { TFunction } from "next-i18next";
import labelsConnectAccounts from "../connect-accounts";
import labelsOpportunities from "../opportunities";
import labelNotifications from "../notifications";

export function mapNotificationsBellLabels(t: TFunction) {
  const notificationsLabels = labelNotifications(t);

  return {
    viewAllNotification: notificationsLabels.viewAllNotification,
    noNewNotifications: notificationsLabels.noNewNotifications,
    notification: notificationsLabels.notification,
    viewNotifications: notificationsLabels.viewNotifications,
    cardLabels: { labels: notificationsLabels.cardLabels, ...mapAdditionalCardLabels(t) }
  };
}

export function mapNotificationsPageLabels(t: TFunction) {
  const notificationsLabels = labelNotifications(t);
  return {
    title: notificationsLabels.title,
    noNotification: {
      title: notificationsLabels.noNotificationTitle,
      description: notificationsLabels.noNotificationDescription
    },
    cardLabels: { labels: notificationsLabels.cardLabels, ...mapAdditionalCardLabels(t) }
  };
}

function mapAdditionalCardLabels(t: TFunction) {
  const accountLabels = labelsConnectAccounts(t).accounts;
  // We need to turn the keys into valid account types
  const accounts = Object.keys(accountLabels).reduce(function (result, key) {
    result[key.toUpperCase()] = accountLabels[key];
    return result;
  }, {});
  const supportACreator = labelsOpportunities(t).supportACreator;

  return { accounts, supportACreator };
}
