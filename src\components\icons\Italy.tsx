import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const Italy: FC<SvgProps> = ({ className = "icon", ...props }) => {
  const { configuration: config } = useDependency();
  return <img className={className} alt="Italy flag" src={`${config.BASE_PATH}/img/flags/it.png`} {...props} />;
};

export default Italy;
