import React, { memo, ReactNode } from "react";
import Head from "next/head";
import ProgramSideNavigation from "./ProgramSideNavigation";

export type FooterLabels = {
  how: string;
  reward: string;
  perks: string;
  faq: string;
  policies: string;
  legal: string;
  disclaimer: string;
  updates: string;
  terms: string;
  privacy: string;
  rights: string;
  report: string;
  copyright: string;
  faqs: string;
  disclosure: string;
  policy: string;
};

export type HeaderLabels = {
  creatorNetwork: string;
  home: string;
  how: string;
  works: string;
  perks: string;
  rewards: string;
  signIn: string;
  logIn: string;
  apply: string;
  applyNow: string;
  requestToJoin: string;
  dashboard: string;
  opportunities: string;
  myContent: string;
  about: string;
  notifications: string;
  myProfile: string;
  signout: string;
  faq: string;
  faqs: string;
  disclosure: string;
  policy: string;
  calendar: string;
};

export type LayoutHeaderProps = {
  pageTitle: string;
  children?: ReactNode;
};

const ogTitle = "EA Support a Creator Program | Electronic Arts";
const description =
  "Join the EA Support-A-Creator program to promote your favorite EA games and earn rewards. Sign up and get started today.";
const canonicalUrl = "https://www.ea.com/support-a-creator";
const ogImage = "https://www.ea.com/support-a-creator/favicon-192x192.png";

export const LayoutHeader = memo(function LayoutHeader({ pageTitle, children }: LayoutHeaderProps) {
  return (
    <>
      <Head>
        <title>{pageTitle || "Support a Creator"}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content="Support-A-Creator, EA Creator Program, Earn with EA games" />
        <link rel="canonical" href={canonicalUrl} />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:title" content={ogTitle} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content={ogImage} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta property="twitter:domain" content="ea.com" />
        <meta property="twitter:url" content={canonicalUrl} />
        <meta name="twitter:title" content={ogTitle} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={ogImage} />
      </Head>
      {children}
    </>
  );
});

export const LayoutFooter = memo(function LayoutFooter({ children }: { children: ReactNode }) {
  return <div className="layout-footer-container">{children}</div>;
});

export const LayoutBody = memo(function LayoutBody({
  children,
  showSideNavigation,
  className = ""
}: {
  children: ReactNode;
  className?: string;
  showSideNavigation?: boolean;
}) {
  return showSideNavigation ? (
    <div className="layout-body">
      <ProgramSideNavigation />
      <div className="content-area">
        <div className={className}>{children}</div>
      </div>
    </div>
  ) : (
    <div className={className}>{children}</div>
  );
});

export default memo(function Layout({ children }: { children: ReactNode }) {
  return <div>{children}</div>;
});
