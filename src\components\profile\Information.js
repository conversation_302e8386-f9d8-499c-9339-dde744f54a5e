import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Form from "../Form";
import CreatorsService from "../../../src/services/CreatorsService";
import MailingAddressForm from "./forms/MailingAddressForm";
import PersonalInformationForm from "./forms/PersonalInformationForm";
import MiscellaneousForm from "./forms/MiscellaneousForm";
import ProfileCard from "./ProfileCard";
import CreatorForm from "../FormRules/CreatorForm";
import LegalEntityForm from "./forms/LegalEntityForm";
import Loading from "../Loading";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../../utils";
import { useRouter } from "next/router";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../context/DependencyContext";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

export default memo(function Information({
  infoLabels,
  buttons,
  user,
  creator,
  updateCreator,
  hardwarePartners,
  countries,
  layout,
  analytics,
  allCountries
}) {
  const {
    errorHandler,
    configuration: { FLAG_PER_PROGRAM_PROFILE, PROGRAM_CODE, FLAG_CREATORS_API_WITH_PROGRAM, DEFAULT_AVATAR_IMAGE },
    client,
    creatorsClient
  } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const router = useRouter();
  const { dispatch, state: { isValidationError, validationErrors, isError } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const [isAccountInformationSaved, setIsAccountInformationSaved] = useState(false);
  const [isMailingAddressSaved, setIsMailingAddressSaved] = useState(false);
  const [isAdditionalInformationSaved, setIsAdditionalInformationSaved] = useState(false);
  const [isLegalEntitySaved, setIsLegalEntitySaved] = useState(false);
  const [accountInformation, setAccountInformation] = useState(null);
  const [registrationDate, setRegistrationDate] = useState(null);
  const [mailingAddress, setMailingAddress] = useState(null);
  const [legalEntity, setLegalEntity] = useState(true);
  const [additionalInformation, setAdditionalInformation] = useState(null);
  const rules = useMemo(() => CreatorForm.rules(infoLabels), [infoLabels]);
  const creatorService = new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE);

  const submitAccountInformation = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        const formData = { ...accountInformation, ...data };
        const updatedAccountInformation = {
          ...formData,
          dateOfBirth: LocalizedDate.format(formData.dateOfBirth, "YYYY-MM-DD")
        };
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              accountInformation: updatedAccountInformation,
              program: {
                code: PROGRAM_CODE
              }
            })
          : FLAG_CREATORS_API_WITH_PROGRAM
          ? await CreatorsService.updatePrefferedValues(client, {
              accountInformation: updatedAccountInformation,
              creatorConnectedProgram: PROGRAM_CODE
            })
          : await CreatorsService.update({ accountInformation: updatedAccountInformation });

        analytics.updatedBasicInformation({ locale: router.locale });
        formData.dateOfBirth = LocalizedDate.fromFormattedDate(formData.dateOfBirth);
        setAccountInformation(formData);
        creator.accountInformation = formData;
        updateCreator(creator);
        setIsAccountInformationSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isAccountInformationSaved, accountInformation, stableDispatch]
  );
  const submitMailingAddress = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        const mailingAddress = { ...data, country: { code: data.country.value, name: data.country.name } };
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              mailingAddress,
              program: {
                code: PROGRAM_CODE
              }
            })
          : FLAG_CREATORS_API_WITH_PROGRAM
          ? await CreatorsService.updatePrefferedValues(client, {
              mailingAddress: data,
              creatorConnectedProgram: PROGRAM_CODE
            })
          : await CreatorsService.update({ mailingAddress: data });

        analytics.updatedBasicInformation({ locale: router.locale });
        setMailingAddress(mailingAddress);
        creator.mailingAddress = mailingAddress;
        updateCreator(creator);
        setIsMailingAddressSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isMailingAddressSaved, mailingAddress, stableDispatch]
  );
  const submitAdditionalInformation = useCallback(
    async (data) => {
      try {
        const additionalInformation = {
          hoodieSize: data.hoodieSize.value,
          hardwarePartners: data.hardwarePartners.map((item) => {
            return {
              id: item.value,
              name: item.label
            };
          })
        };
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              additionalInformation,
              program: {
                code: PROGRAM_CODE
              }
            })
          : FLAG_CREATORS_API_WITH_PROGRAM
          ? await CreatorsService.updatePrefferedValues(client, {
              additionalInformation: data,
              creatorConnectedProgram: PROGRAM_CODE
            })
          : await CreatorsService.update({ additionalInformation: data });

        analytics.updatedBasicInformation({ locale: router.locale });
        if (FLAG_PER_PROGRAM_PROFILE) {
          data.hardwarePartners = data.hardwarePartners.map((item) => {
            return {
              ...item,
              id: item.value,
              name: item.label
            };
          });
        }
        data.hoodieSize = data.hoodieSize.value;
        setAdditionalInformation(data);
        creator.additionalInformation = data;
        updateCreator(creator);
        setIsAdditionalInformationSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isAdditionalInformationSaved, stableDispatch]
  );
  const submitLegalEntity = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        const legalInformation = {
          ...data,
          entityType: data.entityType.value,
          country: { code: data.country.value, name: data.country.name }
        };
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              legalInformation,
              program: {
                code: PROGRAM_CODE
              }
            })
          : FLAG_CREATORS_API_WITH_PROGRAM
          ? await CreatorsService.updatePrefferedValues(client, {
              legalInformation: data,
              creatorConnectedProgram: PROGRAM_CODE
            })
          : await CreatorsService.update({ legalInformation: data });

        analytics.updatedBasicInformation({ locale: router.locale });
        setLegalEntity(data);
        creator.legalInformation = data;
        updateCreator(creator);
        setIsLegalEntitySaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isLegalEntitySaved, stableDispatch]
  );
  const { pending: pendingAccUpd, execute: onSubmitAccountInformation } = useAsync(submitAccountInformation, false);
  const { pending: pendingMailAddUpd, execute: onSubmitMailingAddress } = useAsync(submitMailingAddress, false);
  const { pending: pendingAddInfoUpd, execute: onSubmitAdditionalInformation } = useAsync(
    submitAdditionalInformation,
    false
  );
  const { pending: pendingLegAddUpd, execute: onSubmitLegalEntity } = useAsync(submitLegalEntity, false);

  const accountInformationOnChange = useCallback(() => setIsAccountInformationSaved(false), []);
  const mailingAddressOnChange = useCallback(() => setIsMailingAddressSaved(false), []);
  const additionalInformationOnChange = useCallback(() => setIsAdditionalInformationSaved(false), []);
  const legalEntityOnChange = useCallback(() => setIsLegalEntitySaved(false), []);

  useEffect(() => {
    setAccountInformation(creator.accountInformation);
    setRegistrationDate(creator.formattedRegistrationDate(router.locale));
    setMailingAddress(creator.mailingAddress);
    setAdditionalInformation(creator.additionalInformation);
    setLegalEntity(FLAG_PER_PROGRAM_PROFILE ? creator.legalInformation : creator.legalEntity);
  }, [creator]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(validationErrors)}
          closeButtonAriaLabel={buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const onChangeAsMailingAddress = useCallback(
    (data, isChecked) => {
      isChecked && setLegalEntity({ ...data, ...mailingAddress });
    },
    [mailingAddress]
  );

  return (
    (!legalEntity && (
      <div className="loader">
        <Loading />
      </div>
    )) ||
    (legalEntity && (
      <div className="profile-information">
        {accountInformation && (
          <ProfileCard
            {...{
              labels: { ...infoLabels, buttons: buttons },
              user,
              registrationDate,
              accountInformation,
              creator: FLAG_PER_PROGRAM_PROFILE ? creator : undefined,
              data: layout?.toolTip?.badge,
              locale: router.locale,
              stableDispatch
            }}
          />
        )}
        {rules && accountInformation && (
          <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm
              {...{
                infoLabels,
                rules,
                creator,
                accountInformation,
                buttons,
                onChange: accountInformationOnChange,
                isSaved: isAccountInformationSaved,
                isLoader: pendingAccUpd
              }}
            />
          </Form>
        )}
        {allCountries && mailingAddress && rules && (
          <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm
              {...{
                infoLabels,
                rules,
                mailingAddress,
                allCountries,
                buttons,
                onChange: mailingAddressOnChange,
                isSaved: isMailingAddressSaved,
                isLoader: pendingMailAddUpd
              }}
            />
          </Form>
        )}
        {countries && legalEntity && rules && (
          <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm
              {...{
                infoLabels,
                rules,
                legalEntity,
                countries,
                buttons,
                onChangeAsMailingAddress,
                onChange: legalEntityOnChange,
                isSaved: isLegalEntitySaved,
                isLoader: pendingLegAddUpd
              }}
            />
          </Form>
        )}
        {hardwarePartners && additionalInformation && rules && (
          <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm
              {...{
                infoLabels,
                rules,
                additionalInformation,
                hardwarePartners,
                buttons,
                onChange: additionalInformationOnChange,
                isSaved: isAdditionalInformationSaved,
                isLoader: pendingAddInfoUpd
              }}
            />
          </Form>
        )}
      </div>
    ))
  );
});
