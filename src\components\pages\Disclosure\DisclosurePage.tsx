import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import React, { FC } from "react";

export type DisclosurePageProps = {
  title: string;
  heading1: string;
  paragraph1: string;
  user?: AuthenticatedUser;
};

export const DisclosurePage: FC<DisclosurePageProps> = ({ title, heading1, paragraph1, user }) => {
  return (
    <main className={`disclosure-parent-wrapper ${!user ? "disclosure-wrapper-authenticated" : ""}`}>
      <div className={`disclosure-wrapper`}>
        <div className="disclosure-additional-container">
          <div className="disclosure-additional-container-title">{title}</div>
        </div>
      </div>
      <div className="disclosure-additional-content-container">
        <div className="disclosure-additional-content">
          <h3 className="disclosure-additional-content-sub-title">{heading1}</h3>
          <p className="disclosure-additional-content-paragraph">{paragraph1}</p>
        </div>
      </div>
    </main>
  );
};
