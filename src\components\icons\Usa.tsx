import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const Usa: FC<SvgProps> = ({ className = "icon", ...props }) => {
  const { configuration: config } = useDependency();
  return <img className={className} alt="USA flag" src={`${config.BASE_PATH}/img/flags/US.png`} {...props} />;
};

export default Usa;
