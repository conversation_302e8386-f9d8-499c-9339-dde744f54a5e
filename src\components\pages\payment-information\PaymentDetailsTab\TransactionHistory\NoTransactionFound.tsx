import React, { FC, ReactNode } from "react";
import { Icon, PaymentIcon } from "@eait-playerexp-cn/core-ui-kit";

export type NoTransactionFoundProps = {
  message?: string;
  children?: string | ReactNode;
};

const NoTransactionFound: FC<NoTransactionFoundProps> = ({ message = "", children = "" }) => {
  return (
    <section className="transaction-grid-no-data-section">
      <Icon icon={PaymentIcon} className="transaction-grid-no-data-icon" id="transaction-grid-no-data-icon" />
      {message && <h5 className="transaction-grid-no-data-title"> {message}</h5>}
      {children && <div className="transaction-grid-no-data-desc">{children}</div>}
    </section>
  );
};

export default NoTransactionFound;
