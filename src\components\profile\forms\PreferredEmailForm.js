import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../../formTitle/FormTitle";
import { Input } from "@eait-playerexp-cn/core-ui-kit";

const PreferredEmailForm = ({
  translations,
  rules,
  communicationPreferences,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}) => {
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timetoDisplay = Math.min(Math.max(translations.success.preferredEmail.length * 50, 2000), 7000);

  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [onChange]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved, isEdit]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast
            header={translations.success.updatedInformationHeader}
            content={translations.success.preferredEmail}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            autoClose: timetoDisplay
          }
        )}
      <div className="profile-preferred-email">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={translations.labels.preferredEmailAddressTitle} />
          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>
        <div className="profile-preferred-email-description">
          {translations.labels.preferredEmailAddressDescription}
        </div>

        {!isEdit && <div className="communication-preference-field-title">{translations.labels.preferredEmail}</div>}
        <div className="communication-preference-field">
          {!isEdit && communicationPreferences.email}
          {isEdit && (
            <Controller
              control={control}
              name="email"
              rules={rules.preferredEmail}
              defaultValue={communicationPreferences.email}
              render={({ field, fieldState: { error } }) => (
                <Input
                  dark
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={translations.labels.preferredEmail}
                  label={translations.labels.preferredEmail}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default PreferredEmailForm;
