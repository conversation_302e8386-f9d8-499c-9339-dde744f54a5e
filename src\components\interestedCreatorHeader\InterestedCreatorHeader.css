.interested-creator-header-container {
  @apply w-full;
}
.interested-creator-header {
  @apply flex w-full items-center pt-meas23 text-gray-10 md:!px-meas15 md:pt-meas28;
}
.interested-creator-header-back {
  @apply block flex-1 justify-start xl:hidden;
}
.interested-creator-header-logo {
  @apply hidden w-full font-display-regular font-bold xs:text-mobile-h5 md:block 
  md:text-tablet-h5 lg:ml-meas0 lg:text-left lg:text-desktop-h5 xl:flex-1 xl:justify-start;
}
.interested-creator-header-logo-text {
  @apply ml-meas5 hidden align-middle font-display-bold md:inline;
}
.interested-creator-header-close {
  @apply flex w-full justify-end px-meas5 md:w-auto md:px-meas0;
}
.interested-creator-header-logo .icon-block {
  @apply m-meas4 inline;
}
.interested-creator-header-logo .ea-logo {
  @apply inline h-meas16 w-meas16 fill-gray-10;
}
/* .interested-creator-layout > .mg-container {
  @apply bg-interested-creator-layout-default pl-meas0 pr-meas0;
} */
.interested-creator-layout .mg-bg {
  @apply bg-cover;
}
.interested-creator .breadcrumb-item-label {
  @apply text-caption-small md:text-body-default;
}
.interested-creator .form-error-message,
.interested-creator .select-error-message {
  @apply text-error-50;
}
/* To override the default native behavior of iOS button appearance & make the link as block */
.interested-creator-layout a.btn {
  -webkit-appearance: none;
  display: inline-block;
}

.interested-creators-ui .form-error-message {
  @apply w-full text-center;
}
