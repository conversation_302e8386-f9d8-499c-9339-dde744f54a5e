import React, { FC, memo, MouseEventHandler, MutableRefObject, useCallback, useMemo, useRef } from "react";
import {
  Button,
  Input,
  ModalBody,
  ModalClose<PERSON>utton,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON><PERSON>le,
  ModalV2,
  Toast,
  useToast
} from "@eait-playerexp-cn/core-ui-kit";
import Form from "../Form";
import { Controller, useFormContext } from "react-hook-form";
import Textarea from "../Textarea";
import { useAppContext } from "../../context";
import { onToastClose, SUCCESS, useAsync } from "../../utils";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { CreatorProps, PocLabels } from "./PointOfContactDetails";
import BrowserAnalytics from "../../analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";
import PointOfContactService from "@src/services/PointOfContactService";

type FooterProps = {
  pocLabels: PocLabels;
  pending: boolean;
  closeModal: MouseEventHandler<HTMLButtonElement>;
  cancelButtonRef?: MutableRefObject<HTMLButtonElement | null>;
};

type PointOfContactEmailModalProps = {
  pocLabels: PocLabels;
  pocName: string;
  analytics: BrowserAnalytics;
  buttons: { close: string };
  creator?: CreatorProps;
  closeModal: () => void;
};

const SendEmailToPointOfContactInput: FC<PocLabels> = memo(function SendEmailToPointOfContactInput({ ...pocLabels }) {
  const { control } = useFormContext();
  const rules = useMemo(() => {
    return {
      subject: {
        required: pocLabels.messages.subject,
        maxLength: { value: 255, message: pocLabels.messages.subjectTooLong }
      },
      body: {
        required: pocLabels.messages.body,
        maxLength: { value: 32000, message: pocLabels.messages.bodyTooLong }
      }
    };
  }, [pocLabels]);

  return (
    <>
      <Controller
        control={control}
        name="subject"
        rules={rules.subject}
        render={({ field, fieldState: { error } }) => (
          <Input
            errorMessage={(error && error.message) || ""}
            {...field}
            type="text"
            label={pocLabels.labels.subject}
            placeholder={pocLabels.labels.subject}
            id="subject"
          />
        )}
      />

      <Controller
        control={control}
        name="body"
        rules={rules.body}
        render={({ field, fieldState: { error } }) => (
          <Textarea
            errorMessage={(error && error.message) || ""}
            {...field}
            label={pocLabels.labels.body}
            placeholder={pocLabels.labels.body}
          />
        )}
      />
    </>
  );
});

const FooterButtons: FC<FooterProps> = memo(function FooterButtons({
  closeModal,
  pocLabels,
  pending,
  cancelButtonRef
}) {
  const { formState } = useFormContext();
  return (
    <>
      <Button variant="tertiary" dark size="sm" onClick={closeModal} disabled={pending} ref={cancelButtonRef}>
        {pocLabels.buttons.cancel}
      </Button>
      <Button
        size="sm"
        type="submit"
        spinner={pending}
        disabled={Object.keys(formState.errors).length !== 0 || formState.isValid === false || pending}
      >
        {pocLabels.buttons.send}
      </Button>
    </>
  );
});

export const PointOfContactEmailModal: FC<PointOfContactEmailModalProps> = ({
  pocLabels,
  pocName = "",
  analytics,
  buttons,
  creator,
  closeModal
}) => {
  const { errorHandler, configuration, client } = useDependency();
  const { t } = useTranslation(["point-of-contact"]);
  const { dispatch } = useAppContext() || {};
  const stableDispatch: (state, action?) => void = useCallback(dispatch, []);
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
  const router = useRouter();
  const { success: successToast } = useToast();
  const PointOfContactServiceInstance = useMemo(() => new PointOfContactService(client), [client]);
  const submitHandler = useCallback(
    async (data) => {
      try {
        await PointOfContactServiceInstance.sendEmailToPOC({
          ...data,
          creatorId: creator.id,
          creatorProgram: configuration.PROGRAM_CODE
        });
        analytics.emailSentToPoc({ locale: router.locale });
        closeModal();
        successToast(
          <Toast
            header={pocLabels.success.modalHeader}
            content={t("point-of-contact:success.modalMessage", {
              ...{ defaultGamerTag: pocName }
            })}
          />,
          {
            onClose: () => onToastClose(SUCCESS, stableDispatch)
          }
        );
      } catch (e) {
        closeModal();
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch]
  );

  const { pending, execute: sendEmail } = useAsync(submitHandler, false);

  return (
    <ModalV2 closeButtonRef={cancelButtonRef}>
      <ModalHeader>
        <ModalTitle>{pocLabels.title}</ModalTitle>
        <ModalCloseButton
          ariaLabel={buttons.close}
          closeButtonRef={cancelButtonRef}
          disabled={pending}
        ></ModalCloseButton>
      </ModalHeader>
      <div className="poc-email-container">
        <Form mode="onChange" onSubmit={sendEmail}>
          <ModalBody>
            <SendEmailToPointOfContactInput {...pocLabels} />
          </ModalBody>
          <ModalFooter>
            <FooterButtons {...{ pocLabels, closeModal, pending, cancelButtonRef }} />
          </ModalFooter>
        </Form>
      </div>
    </ModalV2>
  );
};

export default PointOfContactEmailModal;
