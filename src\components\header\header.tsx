import { useRouter } from "next/router";
import ProgramTopNavigation, { NotificationsLabels, TopNavigationPageLabels } from "../ProgramTopNavigation";
import TopNavBar from "./TopNavBar";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";

type HeaderProps = {
  user?: AuthenticatedUser;
  labels?: { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };
  interestedCreator?: boolean;
};

const Header = ({ user, labels, interestedCreator }: HeaderProps) => {
  const { analytics } = useDependency();
  const router = useRouter();
  const locale = router.locale;

  return (
    <header data-testid="header-container" className="header-container">
      <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
      <ProgramTopNavigation
        interestedCreator={interestedCreator}
        pageLabels={labels}
        analytics={analytics}
        user={user}
      />
    </header>
  );
};
export default Header;
