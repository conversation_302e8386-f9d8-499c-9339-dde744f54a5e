import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const Spain: FC<SvgProps> = ({ className = "icon", ...props }) => {
  const { configuration: config } = useDependency();
  return <img className={className} alt="Spain flag" src={`${config.BASE_PATH}/img/flags/es.png`} {...props} />;
};

export default Spain;
