import {
  dashboard,
  dollarPayment,
  faqs,
  home,
  logout,
  MenuItems,
  TopNavigation,
  useDetectScreen,
  user as userIcon
} from "@eait-playerexp-cn/core-ui-kit";
import React, { ComponentType, memo, useCallback } from "react";
import { useRouter } from "next/router";
import MenuDropdown from "./header/MenuDropdown";
import BrowserAnalytics, { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";
import Loading from "./loading/Loading";
import Link from "next/link";

const UnauthenticatedLinks = ({
  labels: { logIn, signIn, requestToJoin },
  interestedCreator
}: {
  labels: { logIn: string; signIn: string; requestToJoin: string };
  interestedCreator: boolean;
}) => {
  return (
    <div className="topnav-signup-button-container">
      {interestedCreator && (
        <Link
          aria-label="Request to join"
          title="Request to join"
          href="/interested-creators/application-start"
          className="topnav-signup-page-request-to-join btn btn-tertiary btn-md"
        >
          {requestToJoin}
        </Link>
      )}
      <Link
        aria-label="Log in"
        title="Log in"
        href="/api/login"
        className="topnav-signup-page-apply-button btn btn-primary btn-md"
      >
        {interestedCreator ? logIn : signIn}
      </Link>
    </div>
  );
};

// Stryker disable all
const NotificationsBell: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("notifications/NotificationsBell"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);
// Stryker restore all

export type NotificationsLabels = {
  viewAllNotification: string;
  noNewNotifications: string;
  viewNotifications: string;
  cardLabels: {
    labels: {
      OPPORTUNITY_INVITATION: {
        title: string;
        description: string;
      };
      CHANNEL_EXPIRY: {
        title: string;
        description: string;
      };
      CONTENT_SUBMISSION_START: {
        title: string;
        description: string;
      };
      CONTENT_SUBMISSION_END: {
        title: string;
        description: string;
      };
      CONTENT_REJECTED: {
        title: string;
        description: string;
      };
      CONTENT_APPROVED: {
        title: string;
        description: string;
      };
      CREATOR_PAYMENT: {
        title: string;
        description: string;
      };
      CONTENT_FEEDBACK: {
        title: string;
        description: string;
      };
      PROFILE_INCOMPLETE: {
        title: string;
        description: string;
      };
      PROFILE_COMPLETE: {
        title: string;
        description: string;
      };
    };
    accounts: {
      FACEBOOK: string;
      TWITCH: string;
      YOUTUBE: string;
      INSTAGRAM: string;
      TIKTOK: string;
    };
    supportACreator: string;
  };
};

export type TopNavigationPageLabels = {
  signout: string;
  myProfile: string;
  requestToJoin: string;
  logIn: string;
  signIn: string;
  home: string;
  faqs: string;
  dashboard: string;
  notifications: string;
};

type ProgramTopNavigationProps = {
  analytics: BrowserAnalytics;
  user?: AuthenticatedUser;
  pageLabels?: { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };
  interestedCreator?: boolean;
};

export default memo(function ProgramTopNavigation({
  analytics,
  user: authenticatedUser,
  pageLabels,
  interestedCreator
}: ProgramTopNavigationProps) {
  const router = useRouter();
  const isMobile = useDetectScreen(767);
  const {
    notificationsClient,
    configuration: {
      NOTIFICATION_BASE_URLS,
      SINGLE_PROGRAM_NOTIFICATIONS,
      DEFAULT_NOTIFICATION_PROGRAM,
      PROGRAM_CODE,
      MENU_ITEMS,
      FLAG_CREATORS_API_WITH_PROGRAM,
      user
    }
  } = useDependency();
  const isMobileOrTab = useDetectScreen(1279);
  const defaultMenuItem: MenuItems[] = [
    {
      label: MENU_ITEMS[PROGRAM_CODE].label,
      value: PROGRAM_CODE,
      gradients: MENU_ITEMS[PROGRAM_CODE].gradients
    }
  ];
  const menuItems =
    FLAG_CREATORS_API_WITH_PROGRAM && user?.programs
      ? user.programs.map((program) => ({
          label: MENU_ITEMS[program].label,
          value: program,
          gradients: MENU_ITEMS[program].gradients
        }))
      : defaultMenuItem;

  const { commonPageLabels, notificationsBellLabels } = pageLabels;

  const header = {
    signout: commonPageLabels.signout,
    myProfile: commonPageLabels.myProfile
  };

  const onMenuSelect = useCallback((item) => {
    if (item.value) {
      localStorage.setItem("programCode", item.value);
    }
  }, []);

  const onNotficationBellClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    router.push("/notifications");
  };

  const NotificationBellContianer = () => {
    return isMobile ? (
      <div className="notificaion-bell-parent-container" onClick={onNotficationBellClick}>
        <NotificationsBell
          labels={notificationsBellLabels}
          locale={router.locale}
          configuration={{
            client: notificationsClient,
            programHosts: NOTIFICATION_BASE_URLS,
            program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
            defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
          }}
        />
        <span className="notification-bell-text">{commonPageLabels.notifications}</span>
      </div>
    ) : (
      <NotificationsBell
        labels={notificationsBellLabels}
        locale={router.locale}
        configuration={{
          client: notificationsClient,
          programHosts: NOTIFICATION_BASE_URLS,
          program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
          defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
        }}
      />
    );
  };

  const AvatarContainer = () => {
    return !isMobileOrTab ? (
      <div className="topnav-profile-avatar-container">
        <MenuDropdown labels={header} analytics={analytics}>
          <img
            alt=""
            data-testid="profile-image"
            src={authenticatedUser.avatar}
            className="topnav-navigation-button-image"
          />
        </MenuDropdown>
      </div>
    ) : null;
  };

  const additionalLabels = {
    requestToJoin: commonPageLabels.requestToJoin,
    logIn: commonPageLabels.logIn,
    signIn: commonPageLabels.signIn
  };

  const additionalLinkContainer = !isMobileOrTab
    ? [
        {
          children: <UnauthenticatedLinks labels={additionalLabels} interestedCreator={interestedCreator} />,
          active: false
        }
      ]
    : [];

  const topNavButtons =
    authenticatedUser?.status === "ACTIVE"
      ? [
          {
            button: {
              title: "Payment",
              onClick: () => {
                router.push("/payment-information");
              },
              asset: dollarPayment
            },
            active: router.pathname === "/payment-information"
          },
          {
            children: <NotificationBellContianer />,
            active: router.pathname === "/notifications"
          },
          {
            children: <AvatarContainer />,
            active: router.pathname === "/profile"
          }
        ]
      : additionalLinkContainer;

  const affiliateButtons =
    authenticatedUser?.status !== "ACTIVE"
      ? [
          {
            id: "home",
            label: commonPageLabels.home,
            icon: home,
            onClick: () => router.push("/"),
            active: router.pathname === "/"
          },
          {
            id: "faq",
            label: commonPageLabels.faqs,
            icon: faqs,
            onClick: () => router.push("/faq"),
            active: router.pathname === "/faq"
          },
          ...(interestedCreator
            ? [
                {
                  id: "request-to-join",
                  label: commonPageLabels.requestToJoin,
                  icon: userIcon,
                  onClick: () => router.push("/interested-creators/application-start"),
                  active: false
                }
              ]
            : []),
          {
            id: "sign-in",
            label: interestedCreator ? commonPageLabels.logIn : commonPageLabels.signIn,
            icon: logout,
            onClick: () => router.push("/api/login"),
            active: false
          }
        ]
      : [
          {
            id: "dashboard",
            label: commonPageLabels.dashboard,
            icon: dashboard,
            onClick: () => router.push("/dashboard"),
            active: router.pathname === "/dashboard"
          },
          {
            id: "faqs",
            label: commonPageLabels.faqs,
            icon: faqs,
            onClick: () => router.push("/faq"),
            active: router.pathname === "/faq"
          },
          {
            id: "myProfile",
            label: header.myProfile,
            icon: userIcon,
            onClick: () => router.push("/profile"),
            active: router.pathname === "/profile"
          },
          {
            id: "logout",
            label: header.signout,
            icon: logout,
            onClick: () => router.push("/api/logout"),
            active: false
          }
        ];
  const leftNavMenuItems =
    authenticatedUser?.status !== "ACTIVE" && !isMobileOrTab
      ? [
          {
            id: "home",
            label: commonPageLabels.home,
            icon: home,
            onClick: () => router.push("/"),
            active: router.pathname === "/"
          },
          {
            id: "faq",
            label: commonPageLabels.faqs,
            icon: faqs,
            onClick: () => router.push("/faq"),
            active: router.pathname === "/faq"
          }
        ]
      : [];

  return (
    <div className="header-topnav-parent-container">
      <TopNavigation
        dropdownMenu={{
          dropdownLabel: menuItems.find((item) => item.value === PROGRAM_CODE)?.label,
          onMenuItemSelect: onMenuSelect,
          menuItems
        }}
        rightNavigationButtons={topNavButtons}
        sideNavButtons={affiliateButtons}
        leftNavigationButtons={leftNavMenuItems}
      />
    </div>
  );
});
