import TimeRangeFilterForm, {
  FilterLabels,
  PaymentFilterProps,
  PaymentsCriteria,
  SelectOptions
} from "@components/forms/timeRangeFilterForm/TimeRangeFilterForm";
import { FilterPill } from "@eait-playerexp-cn/core-ui-kit";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import React, { FC } from "react";
import cx from "classnames";
import { DefaultPaymentDateRange } from "@src/services/paymentsStatistics/PaymentsService";

export type Labels = {
  filteredBy: string;
  filterPillLabel: string;
};
export type DashboardFilterProps = {
  labels: Labels & FilterLabels;
  launchDate: string;
  selectedDate: string;
  onClickFilterPill: () => void;
  dateRangeOptions: SelectOptions;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  selectedFilters: PaymentFilterProps[];
  selectedCriteria: PaymentsCriteria;
  updatePaymentsFilterDetails: (formData) => void;
  analytics: BrowserAnalytics;
  isFilter: boolean;
  format: string;
  screenOrientation: boolean;
};
const DashboardFilter: FC<DashboardFilterProps> = ({
  dateRangeOptions,
  labels,
  defaultPaymentDateRange,
  selectedCriteria,
  selectedFilters,
  updatePaymentsFilterDetails,
  launchDate,
  analytics,
  onClickFilterPill,
  selectedDate,
  isFilter,
  format,
  screenOrientation
}) => {
  const { filterPillLabel, filteredBy } = labels;
  return (
    <div className="dashboard-filter-wrapper">
      <div className="dashboard-filter-pills-status">
        <span
          className={cx({
            "dashboard-filter-pills-text": !screenOrientation,
            "dashboard-filter-pills-text-not-active": !isFilter,
            "dashboard-filter-pills-text-active": screenOrientation
          })}
        >
          {filteredBy} :{" "}
        </span>
        {isFilter && <FilterPill filterLabel={filterPillLabel} onClick={onClickFilterPill} />}
        <span
          className={cx({
            "dashboard-filter-pills-date": !screenOrientation,
            "dashboard-filter-pills-date-active": screenOrientation
          })}
        >
          {selectedDate}
        </span>
      </div>
      <div className="dashboard-filter-button">
        <TimeRangeFilterForm
          {...{
            filterLabels: labels,
            dateRangeOptions,
            defaultPaymentDateRange,
            AFFILIATE_LAUNCH_DATE: launchDate,
            selectedCriteria,
            selectedFilters,
            updatePaymentsFilterDetails,
            analytics,
            format,
            isDateDisabled: true
          }}
        />
      </div>
    </div>
  );
};

export default DashboardFilter;
