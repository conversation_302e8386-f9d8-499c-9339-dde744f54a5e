import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../../formTitle/FormTitle";
import { Input } from "@eait-playerexp-cn/core-ui-kit";

const PreferredPhoneNumberForm = ({
  translations,
  rules,
  communicationPreferences,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}) => {
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timetoDisplay = Math.min(Math.max(translations.success.preferredPhoneNumber.length * 50, 2000), 7000);

  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [onChange]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved, isEdit]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast
            header={translations.success.updatedInformationHeader}
            content={translations.success.preferredPhoneNumber}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            autoClose: timetoDisplay
          }
        )}
      <div className="profile-preferred-phone">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={translations.labels.preferredPhoneNumberTitle} />
          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        {!isEdit && (
          <div className="communication-preference-field-title">{translations.labels.preferredPhoneNumber}</div>
        )}
        <div className="communication-preference-field">
          {!isEdit && communicationPreferences.phone}
          {isEdit && (
            <Controller
              control={control}
              name="phone"
              rules={rules.preferredPhoneNumber}
              defaultValue={communicationPreferences.phone}
              render={({ field, fieldState: { error } }) => (
                <Input
                  dark
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  type="text"
                  placeholder={translations.labels.preferredPhoneNumber}
                  label={translations.labels.preferredPhoneNumber}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default PreferredPhoneNumberForm;
