import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction";
import Search from "../../Search";
import { MultiSelect } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../../formTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const FranchiseYouPlayForm = ({
  franchisesYouPlayLabels,
  buttons,
  onChange,
  isSaved = false,
  franchises,
  preferredPrimaryFranchises,
  preferredSecondaryFranchises,
  isLoader
}) => {
  const { configuration } = useDependency();
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const [primaryFranchiseImage, setPrimaryFranchiseImage] = useState(
    preferredPrimaryFranchises && preferredPrimaryFranchises.image
  );
  const { success: successToast } = useToast();
  let modalSuccessText = franchisesYouPlayLabels.messages.success.content;
  const timeToDisplay = Math.min(Math.max(modalSuccessText.length * 50, 2000), 7000);
  const [secondaryFranchiseSelection, setSecondaryFranchiseSelection] = useState(preferredSecondaryFranchises);
  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [isEdit]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isEdit &&
        isSaved &&
        successToast(
          <Toast
            header={franchisesYouPlayLabels.messages.success.header}
            content={modalSuccessText}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            autoClose: { timetoDisplay: timeToDisplay }
          }
        )}
      <div className="profile-game-preferences-title-actions">
        <FormTitle title={franchisesYouPlayLabels.title} />
        <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
      </div>
      <div className="profile-game-preferences-description">{franchisesYouPlayLabels.description}</div>
      <div className="profile-game-preferences-primary-container">
        <h4 className="profile-game-preferences-primary-title">{franchisesYouPlayLabels.primaryFranchiseTitle}</h4>
        <div className="profile-game-preferences-primary-subtitle">
          {franchisesYouPlayLabels.primaryFranchiseSubTitle}
        </div>
        <div className="profile-game-preferences-primary-show">
          {!isEdit && preferredPrimaryFranchises && (
            <>
              <div className="profile-game-preferences-tag">{preferredPrimaryFranchises.label}</div>
              <div className="profile-game-preferences-option">
                <img
                  alt="Franchise image"
                  className="profile-game-preferences-option-image"
                  src={`${configuration.BASE_PATH}${preferredPrimaryFranchises.image}`}
                />
              </div>
            </>
          )}
          {isEdit && (
            <Controller
              control={control}
              name="primaryFranchise"
              defaultValue={preferredPrimaryFranchises}
              render={({ field, fieldState: { error } }) => (
                <>
                  <Search
                    errorMessage={(error && error.message) || ""}
                    {...field}
                    onChange={(item) => {
                      setPrimaryFranchiseImage(item.image);
                      field.onChange(item);
                    }}
                    options={franchises}
                    placeholder={franchisesYouPlayLabels.labels.primaryFranchise}
                  />
                  <div className="profile-game-preferences-option">
                    {primaryFranchiseImage && (
                      <img
                        alt="Franchise image"
                        className="profile-game-preferences-option-image"
                        src={`${configuration.BASE_PATH}${primaryFranchiseImage}`}
                      />
                    )}
                  </div>
                </>
              )}
            />
          )}
        </div>
      </div>
      <div className="profile-game-preferences-secondary-container">
        <h4 className="profile-game-preferences-secondary-title">{franchisesYouPlayLabels.secondaryFranchiseTitle}</h4>
        <div className="profile-game-preferences-secondary-subtitle">
          {franchisesYouPlayLabels.secondaryFranchiseSubTitle}
        </div>
        <div className="profile-secondary-game-preferences-show">
          {!isEdit && (
            <>
              <div className="profile-secondary-game-preferences-tags">
                {preferredSecondaryFranchises.map((item, index) => (
                  <div className="profile-game-preferences-tag" key={`secondary-franchise-label-${index}`}>
                    {item.label}
                  </div>
                ))}
              </div>
              <div className="profile-secondary-game-preferences-options">
                {preferredSecondaryFranchises.map((item, index) => (
                  <SecFranchiseCard key={`secondary-franchise-card-${index}`} franchise={item} />
                ))}
              </div>
            </>
          )}
          {isEdit && (
            <Controller
              control={control}
              name="secondaryFranchise"
              defaultValue={preferredSecondaryFranchises}
              render={({ field, fieldState: { error } }) => (
                <>
                  <MultiSelect
                    {...field}
                    selectedOptions={field.value}
                    options={franchises}
                    errorMessage={(error && error.message) || ""}
                    placeholder={franchisesYouPlayLabels.secondaryFranchiseTitle}
                    onChange={(selectedData) => {
                      setSecondaryFranchiseSelection(selectedData);
                      field.onChange(selectedData);
                    }}
                  />
                  <div className="profile-secondary-game-preferences-options">
                    {secondaryFranchiseSelection.map((item, index) => (
                      <SecFranchiseCard key={`secondary-franchise-card-${index}`} franchise={item} />
                    ))}
                  </div>
                </>
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default FranchiseYouPlayForm;

const SecFranchiseCard = ({ franchise }) => {
  const { configuration } = useDependency();
  return (
    <div className="profile-secondary-franchise-option">
      <img
        alt="Franchise image"
        className="profile-secondary-franchise-option-image"
        src={
          franchise.image
            ? `${configuration.BASE_PATH}${franchise.image}`
            : `${configuration.BASE_PATH}/img/franchises-you-play/FIFA.png`
        }
      />
      <div className="profile-secondary-franchise-option-text">{franchise.label}</div>
    </div>
  );
};
