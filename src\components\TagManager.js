import { memo } from "react";
import { ampli } from "../../analytics/browser/src/ampli";
import { sessionReplayPlugin } from "@amplitude/plugin-session-replay-browser";
import { useDependency } from "@src/context/DependencyContext";

const TagManager = () => {
  const {
    configuration: { AMPLITUDE_API_KEY, ANALYTICS_SAMPLE_RATE }
  } = useDependency();
  if (!!AMPLITUDE_API_KEY && typeof window !== "undefined") {
    const loadOptions = {
      client: {
        apiKey: AMPLITUDE_API_KEY,
        configuration: {
          defaultTracking: { session: true }
        }
      }
    };
    const sessionReplayTracking = sessionReplayPlugin({
      sampleRate: ANALYTICS_SAMPLE_RATE
    });
    ampli.load(loadOptions);
    ampli.client.add(sessionReplayTracking);
  }

  return false;
};

export default memo(TagManager);
