{"filters": "Filtres", "dateRange": "Période", "startDate": "Date de début", "endDate": "Date de fin", "paymentStatus": "Statut des paiements", "opportunityType": "Type", "applyFilters": "Appliquer", "startDateError": "La date de début doit être antérieure à la date de fin", "endDateError": "La date de fin doit être postérieure à la date de début", "startDateRequired": "<PERSON><PERSON> devez indiquer une date de début", "endDateRequired": "<PERSON><PERSON> devez indiquer une date de fin", "sameDateError": "La date de début et la date de fin ne peuvent pas être identiques", "range": {"allTime": "Toutes les périodes", "thisMonth": "Ce mois-ci", "past30Days": "Au cours des 30 derniers jours", "past90Days": "Au cours des 90 derniers jours", "past6Months": "Au cours des 6 derniers mois", "yearToDate": "De<PERSON>is le début de l’année", "lastYear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom": "Personnalisation"}, "status": {"all": "<PERSON>ut", "processed": "Traitement effectué", "pending": "En attente"}, "type": {"all": "<PERSON>ut", "opportunity": "Opportunité", "creatorCode": "Code de création", "theSims": "Programme Sims Maker"}}