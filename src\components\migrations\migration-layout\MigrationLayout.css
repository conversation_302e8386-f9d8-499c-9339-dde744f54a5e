.mg-container {
  background: linear-gradient(351deg, #0d1042 74.35%, #930c92 121.62%);
  @apply absolute z-0 flex min-h-full w-full flex-col items-center overflow-y-hidden xs:overflow-x-hidden;
}
.mg-container.mg-loading {
  @apply h-full;
}
.mg-bg {
  @apply absolute h-full w-full bg-migration-background-mobile bg-contain bg-top bg-no-repeat md:bg-migration-background-tablet md:bg-cover xl:bg-migration-shape;
  z-index: -1;
}

.mg-page {
  @apply mb-meas15 flex h-full min-h-screen w-full flex-col items-center text-gray-10;
}

.mg-page form {
  @apply mx-auto flex flex-col items-center justify-center xs:w-[280px] md:w-[640px] xl:w-[672px];
}

.mg-page .onboarding-terms-and-condtions-container {
  @apply xs:pr-meas18;
}
