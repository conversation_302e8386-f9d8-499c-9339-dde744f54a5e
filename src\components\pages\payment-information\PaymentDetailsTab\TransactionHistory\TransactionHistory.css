/* TODO: split this into their corresponding components */
.payment-details-transaction-history-grid {
  @apply w-full rounded-[3px] border border-navy-50 bg-navy-50 xl:min-w-[780px];
  background: hsl(236deg 67% 12% / 90%);
}
.payment-details-transaction-history-grid .content-grid-cell {
  @apply break-all pr-meas4;
}
.payment-details-transaction-history-grid .content-grid-wrapper {
  @apply mb-meas0 w-full pb-meas0 md:w-auto;
}
.payment-details-transaction-history-grid.no-records .content-grid-wrapper {
  @apply pb-meas8;
}

.payment-details-transaction-history-grid.with-pagination .content-grid-last-record {
  @apply border-b-[1px];
}
.transaction-history-grid-header-date-icon {
  @apply flex items-center justify-start;
}
.transaction-history-grid-header-date-icon .tooltip-element {
  @apply mr-[9px];
}
.transaction-history-grid-header-date-icon .tooltip:after {
  @apply w-[290px] bg-none;
}

.transaction-history-desc {
  @apply flex flex-row items-center justify-start;
}
.transaction-history-desc-image {
  @apply mr-[10px] h-meas20 w-[61px] min-w-[61px] rounded-[4px];
}
.transaction-history-desc-title {
  @apply font-text-regular text-white underline xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.transaction-history-contract {
  @apply mr-meas12 flex flex-row items-center justify-end;
}

.transaction-history-success-text .icontext-text,
.transaction-history-success-text .icontext-icon {
  @apply text-success-60;
}

.transaction-grid-no-data-section {
  @apply flex flex-col items-center justify-center p-meas0 md:pb-meas4 md:pt-meas16;
}
.transaction-grid-no-data-icon {
  @apply mb-[10px] h-[40px] w-[40px] fill-navy-30;
}
.transaction-grid-no-data-title {
  @apply font-text-bold leading-6 text-white xs:text-mobile-body-default md:text-tablet-h4  md:leading-7;
}
.transaction-grid-no-data-desc {
  @apply text-center font-text-regular text-white xs:text-mobile-body-small md:text-tablet-body-default md:leading-6;
}
.transaction-grid-no-data-desc a {
  @apply cursor-pointer underline;
}
.transaction-grid-icontext-wrapper {
  @apply flex flex-row items-center;
}
.transaction-grid-icontext-text {
  @apply ml-[10px] text-white xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.transaction-grid-icontext-icon {
  @apply text-white;
}
.transaction-grid-icontext-icon.creator-perk {
  @apply w-[16px];
}

.transaction-history-success-text .transaction-grid-icontext-icon,
.transaction-history-success-text .transaction-grid-icontext-text {
  @apply text-success-40;
}

.transaction-record-block-image {
  @apply h-[68px];
}
.transaction-record-block-amount,
.transaction-record-block-link {
  @apply h-[64px];
}
.mobile-wallet-grid .transaction-grid-icontext-text {
  @apply ml-meas0;
}
