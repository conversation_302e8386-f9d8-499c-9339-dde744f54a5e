import { faqs, grid, SidePanel } from "@eait-playerexp-cn/core-ui-kit";
import React, { memo } from "react";
import { useRouter } from "next/router";
import { useDetectScreen } from "@src/utils";

export default memo(function ProgramSideNavigation() {
  const router = useRouter();
  const isMobileAndTablet = useDetectScreen(1024);
  const affiliateButtons = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: grid,
      onClick: () => router.push("/dashboard"),
      active: router.pathname === "/dashboard"
    },
    {
      id: "faqs",
      label: "FAQs",
      icon: faqs,
      onClick: () => router.push("/faq"),
      active: router.pathname === "/faq"
    }
  ];

  return !isMobileAndTablet ? <SidePanel buttons={affiliateButtons} /> : <></>;
});
