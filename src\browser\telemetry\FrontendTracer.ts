import { CompositePropagator, W3CBaggagePropagator, W3CTraceContextPropagator } from "@opentelemetry/core";
import { BatchSpanProcessor, WebTracerProvider } from "@opentelemetry/sdk-trace-web";
import { registerInstrumentations } from "@opentelemetry/instrumentation";
import { Resource } from "@opentelemetry/resources";
import {
  SEMRESATTRS_DEPLOYMENT_ENVIRONMENT,
  SEMRESATTRS_SERVICE_NAME,
  SEMRESATTRS_SERVICE_VERSION
} from "@opentelemetry/semantic-conventions";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import getConfig from "next/config";
import { NoOpSpanExporter } from "@eait-playerexp-cn/telemetry";

const FrontendTracer = async () => {
  const { ZoneContextManager } = await import("@opentelemetry/context-zone");
  const { publicRuntimeConfig: props = {} } = getConfig();

  const tracerProvider = new WebTracerProvider({
    resource: Resource.default().merge(
      new Resource({
        [SEMRESATTRS_SERVICE_NAME]: "cn-website",
        [SEMRESATTRS_DEPLOYMENT_ENVIRONMENT]: props.APP_ENV || "local",
        [SEMRESATTRS_SERVICE_VERSION]: props.RELEASE_VERSION || "1.0.0.dev"
      })
    )
  });

  const contextManager = new ZoneContextManager();

  tracerProvider.addSpanProcessor(
    new BatchSpanProcessor(
      props.FLAG_OBSERVABILITY === "true" ? new OTLPTraceExporter({ headers: {} }) : new NoOpSpanExporter()
    )
  );

  tracerProvider.register({
    contextManager,
    propagator: new CompositePropagator({
      propagators: [new W3CBaggagePropagator(), new W3CTraceContextPropagator()]
    })
  });

  registerInstrumentations({ tracerProvider });
};

export default FrontendTracer;
