import Link from "next/link";
import React, { memo } from "react";
import { useDependency } from "@src/context/DependencyContext";
import { useDetectScreen } from "@eait-playerexp-cn/core-ui-kit";

export type HomePageLabels = {
  batch?: string;
  heading?: string;
  paragraph1?: string;
  paragraph2?: string;
  paragraph3?: string;
  paragraph4?: string;
  applyLink: string;
  applyDescription: string;
  eaAccountLabel: string;
};

export type CreatorTypeFallBackProps = {
  value: string;
  label: string;
  imageAsIcon: string;
};

export const homePageLabels = {
  welcome: {
    title: "Welcome to the EA",
    supportaCreator: "Support a Creator",
    description:
      "The Electronic Arts Support a Creator Program is designed to reward content creators, streamers, and influencers who share a passion for EA games.",
    applylink: "Request to join EA Support a Creator*",
    applyNote: "To submit, you need to be at least 18 years old and will need an",
    eaAccount: "Electronic Arts Account."
  },
  howIsItWorks: {
    heading: "How does it work?",
    title1: "Request your code",
    description1:
      "Request your unique creator code that best represents your brand or identity and something your audience will easily recognize.",
    title2: "Create & Share",
    description2:
      "Once your creator code is activated, you can start sharing your code anytime when creating content for your audience featuring your favorite EA games.",
    title3: "Get Rewarded",
    description3:
      "Every time your audience enters your creator code when purchasing eligible EA products, you’ll be rewarded with a portion of that sale."
  },
  eligibleCriteria: {
    heading: "Eligibility Criteria",
    description:
      "To join the EA Support a Creator program, you must be 18+ and follow EA's Rules of Conduct. While audience size is one factor, we look at the whole picture. So, if you're passionate and engaged, we encourage you to apply, even if your audience is still growing!",
    title1: "18+ YEARS OLD",
    description1: "Creators must be at least 18 years old.",
    title2: "RULES OF CONDUCT",
    description2: "Creators must follow EA’s Rules of Conduct.",
    title3: "AUDIENCE",
    description3: "Have a strong audience with positive engagement."
  }
};

export default memo(function HomePage() {
  const { configuration: config } = useDependency();
  const isMobile = useDetectScreen(767);

  return (
    <main className="welcome-to-ea-parent-container">
      <section className="welcome-to-ea">
        <div className="welcome-to-ea-container">
          <div className="welcome-to-ea-creators-content-section">
            <h2 className="welcome-to-ea-title">{homePageLabels.welcome.title}</h2>
            <div className="welcome-to-ea-application-name">{homePageLabels.welcome.supportaCreator}</div>
            <p className="welcome-to-ea-description">{homePageLabels.welcome.description}</p>
            <div className="welcome-to-ea-apply">
              <Link href="/interested-creators/application-start" className="welcome-to-ea-apply-link">
                {homePageLabels.welcome.applylink}
              </Link>
              <p className="welcome-to-ea-apply-note">
                {homePageLabels.welcome.applyNote}{" "}
                <Link className="welcome-to-ea-account" href="/api/accounts">
                  {homePageLabels.welcome.eaAccount}
                </Link>
              </p>
            </div>
          </div>
          <div className="welcome-to-ea-creator-image-section">
            <img src={`${config.BASE_PATH}/img/creators.png`} alt="" />
          </div>
        </div>
      </section>
      <section id="how-it-works" className="how-it-works">
        <div className="how-it-works-container">
          <div>
            <div className="how-it-works-heading">{homePageLabels.howIsItWorks.heading}</div>
          </div>

          <div className="how-it-works-steps">
            <div className="how-it-works-steps-creator-code-section">
              <div className="how-it-works-steps-creator-code-image">
                <img src={`${config.BASE_PATH}/img/creator-code.png`} alt="Create your Code" />
              </div>
              <div className="how-it-works-step1">
                <img src={`${config.BASE_PATH}/img/step1.svg`} alt="Step1" />
                <div className="how-it-works-steps-creator-code-line">
                  <img src={`${config.BASE_PATH}/img/line-desktop.svg`} alt="Stepper" />
                </div>
              </div>
              <div className="how-it-works-steps-creator-code">
                <div className="how-it-works-steps-creator-code-title">{homePageLabels.howIsItWorks.title1} </div>
                <div className="how-it-works-steps-creator-code-description">
                  {homePageLabels.howIsItWorks.description1}
                </div>
              </div>
            </div>
            <div className="how-it-works-steps-create-and-share-section">
              {!isMobile ? (
                <div className="how-it-works-steps-create-and-share">
                  <div className="how-it-works-steps-create-and-share-title">{homePageLabels.howIsItWorks.title2} </div>
                  <div className="how-it-works-steps-create-and-share-description">
                    {homePageLabels.howIsItWorks.description2}
                  </div>
                </div>
              ) : (
                <div className="how-it-works-steps-create-and-share-image">
                  <img src={`${config.BASE_PATH}/img/create-and-share.png`} alt="Create and share" />
                </div>
              )}

              <div className="how-it-works-step2">
                <img src={`${config.BASE_PATH}/img/step2.svg`} alt="Step2" />
                <div className="how-it-works-steps-creator-code-line">
                  <img src={`${config.BASE_PATH}/img/line-desktop.svg`} alt="Stepper" />
                </div>
              </div>
              {!isMobile ? (
                <div className="how-it-works-steps-create-and-share-image">
                  <img src={`${config.BASE_PATH}/img/create-and-share.png`} alt="Create and share" />
                </div>
              ) : (
                <div className="how-it-works-steps-create-and-share">
                  <div className="how-it-works-steps-create-and-share-title">{homePageLabels.howIsItWorks.title2} </div>
                  <div className="how-it-works-steps-create-and-share-description">
                    {homePageLabels.howIsItWorks.description2}
                  </div>
                </div>
              )}
            </div>
            <div className="how-it-works-steps-grow-your-sales-section">
              <div className="how-it-works-steps-grow-your-sales-image">
                <img src={`${config.BASE_PATH}/img/sales.png`} alt="Sales" />
              </div>
              <div className="how-it-works-step3">
                <img src={`${config.BASE_PATH}/img/step3.svg`} alt="Step3" />
              </div>
              <div className="how-it-works-steps-grow-your-sales">
                <div className="how-it-works-steps-grow-your-sales-title">
                  {homePageLabels.howIsItWorks.title3}
                  <div className="how-it-works-steps-grow-your-sales-description">
                    {homePageLabels.howIsItWorks.description3}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="eligibility-criteria">
        <div className="eligibility-criteria-container">
          <div className="eligibility-criteria-heading">{homePageLabels.eligibleCriteria.heading}</div>
          <p className="eligibility-criteria-description">{homePageLabels.eligibleCriteria.description}</p>
          <div className="eligibility-criteria-list">
            <div className="eligibility-criteria-list-item">
              <img src={`${config.BASE_PATH}/img/18.svg`} alt="Years old" />
              <div className="eligibility-criteria-list-item-content">
                <h3 className="eligibility-criteria-list-item-years-old-title">
                  {homePageLabels.eligibleCriteria.title1}
                </h3>
                <div className="eligibility-criteria-list-item-years-old-description">
                  {homePageLabels.eligibleCriteria.description1}
                </div>
              </div>
            </div>
            <div className="eligibility-criteria-list-item">
              <img src={`${config.BASE_PATH}/img/conduct.svg`} alt="Code of Conduct" />
              <div className="eligibility-criteria-list-item-content">
                <h3 className="eligibility-criteria-list-item-code-conduct-title">
                  {homePageLabels.eligibleCriteria.title2}
                </h3>
                <div className="eligibility-criteria-list-item-code-conduct-description">
                  {homePageLabels.eligibleCriteria.description2}
                </div>
              </div>
            </div>
            <div className="eligibility-criteria-list-item">
              <img src={`${config.BASE_PATH}/img/audience.svg`} alt="Audience" />
              <div className="eligibility-criteria-list-item-content">
                <h3 className="eligibility-criteria-list-item-audience-title">
                  {homePageLabels.eligibleCriteria.title3}
                </h3>
                <div className="eligibility-criteria-list-item-audience-description">
                  {homePageLabels.eligibleCriteria.description3}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
});
