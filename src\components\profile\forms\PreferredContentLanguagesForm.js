import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction";
import { MultiSelect } from "@eait-playerexp-cn/core-ui-kit";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../../formTitle/FormTitle";

const PreferredContentLanguagesForm = ({
  translations,
  rules,
  communicationPreferences,
  languages,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}) => {
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timetoDisplay = Math.min(Math.max(translations.success.contentLanguage.length * 50, 2000), 7000);

  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [onChange]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved, isEdit]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast
            header={translations.success.updatedInformationHeader}
            content={translations.success.contentLanguage}
            closeButtonAriaLabel={buttons.close}
          />,
          {
            autoClose: timetoDisplay
          }
        )}
      <div className="profile-preferred-content-language">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={translations.labels.contentLanguagesTitle} />

          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>
        <div className="profile-preferred-content-language-description">
          {translations.labels.contentLanguagesDescription}
        </div>

        <div className="communication-preference-field-title">{translations.labels.contentLanguage}</div>
        <div className="communication-preference-content-languages-field">
          {!isEdit &&
            communicationPreferences.contentLanguages &&
            communicationPreferences.contentLanguages.map((language, key) => (
              <div className="language-head" key={key}>
                {language?.label || language?.name}
              </div>
            ))}
        </div>
        {isEdit && (
          <div className="communication-preference-content-languages-field-edit">
            <Controller
              control={control}
              name="contentLanguages"
              rules={rules.contentLanguage}
              defaultValue={communicationPreferences.contentLanguages}
              render={({ field, fieldState: { error } }) => (
                <MultiSelect
                  {...field}
                  selectedOptions={field.value}
                  options={languages}
                  errorMessage={(error && error.message) || ""}
                  placeholder={translations.labels.contentLanguage}
                />
              )}
            />
          </div>
        )}
      </div>
    </>
  );
};
export default PreferredContentLanguagesForm;
