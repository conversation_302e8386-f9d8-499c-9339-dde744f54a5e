import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect } from "react";
import { Button, discordIcon, Icon, Input, MultiSelect, outlineAdd, Select } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import { POPUP_OPENED, WINDOW_PARAMS } from "../../utils";
import { ConnectedAccount } from "./ConnectedAccounts";

const url = "/support-a-creator/api/discord-login";

export const AddDiscordAccount = memo(function AddDiscordAccount({
  translation,
  showAddConfirmation,
  setShowAddConfirmation,
  layout,
  labels,
  setRemoveAccount,
  removeAccount,
  discord,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal
}) {
  const { dispatch } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const handleClick = useCallback(() => {
    if (!showAddConfirmation) {
      setShowAddConfirmation(true);
      const loginWindow = window.open(url, "_blank", WINDOW_PARAMS);
      stableDispatch({ type: POPUP_OPENED, data: true });
      const loop = setInterval(function () {
        if (loginWindow.closed) {
          clearInterval(loop);
          stableDispatch({ type: POPUP_OPENED, data: false });
        }
      }, 100);
    }
  }, [showAddConfirmation, stableDispatch, setShowAddConfirmation]);

  return (
    <div className="mg-communication-row discord">
      <h5 className="mg-communication-title discord">{translation.labels.discordTitle}</h5>
      <div className="mg-communication-description discord">{translation.labels.discordDescription}</div>
      {(discord && (
        <ConnectedAccount
          {...{
            layout,
            labels,
            setRemoveAccount,
            removeAccount,
            accounts: [
              { ...discord, accountIcon: discordIcon, accountType: "DISCORD", type: "DISCORD", username: discord.tag }
            ],
            showAddConfirmation,
            setShowAddConfirmation,
            accountToRemove,
            setAccountToRemove,
            showRemoveAccountModal,
            setShowRemoveAccountModal
          }}
        />
      )) || (
        <Button variant="secondary" size="sm" onClick={handleClick}>
          <Icon icon={outlineAdd} />
          <span>{translation.labels.addDiscord}</span>
        </Button>
      )}
    </div>
  );
});

const CommunicationPreferencesInput = memo(function CommunicationPreferencesInput({
  translation,
  languages,
  locales,
  communications,
  showAddConfirmation,
  setShowAddConfirmation,
  rules,
  labels,
  layout,
  removeAccount,
  setRemoveAccount,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal
}) {
  const methods = useFormContext();
  const { control, setValue } = methods;
  const { discord = null } = communications || {};

  useEffect(() => {
    setValue("preferredLanguage", (communications && communications.preferredLanguage) || locales[0]);
  }, [locales]);

  return (
    <>
      <AddDiscordAccount
        {...{
          translation,
          showAddConfirmation,
          setShowAddConfirmation,
          discord,
          layout,
          labels,
          setRemoveAccount,
          removeAccount,
          accountToRemove,
          setAccountToRemove,
          showRemoveAccountModal,
          setShowRemoveAccountModal
        }}
      />
      <div className="mg-communication-row preferred-email">
        <div className="mg-communication-title preferred-email">{translation.labels.preferredEmailAddressTitle}</div>
        <div className="mg-communication-description preferred-email">
          {translation.labels.preferredEmailAddressDescription}
        </div>
        <div className="communication-preference-field">
          <Controller
            control={control}
            name="email"
            rules={rules.preferredEmail}
            defaultValue={communications.email}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={translation.labels.preferredEmail}
                placeholder={translation.labels.preferredEmail}
              />
            )}
          />
        </div>
      </div>
      <div className="mg-communication-row preferred-phone-number">
        <div className="mg-communication-title preferred-phone-number">
          {translation.labels.preferredPhoneNumberTitle}
        </div>
        <div className="communication-preference-field">
          <Controller
            control={control}
            name="phone"
            rules={rules.preferredPhoneNumber}
            defaultValue={communications.phone}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={(error && error.message) || ""}
                {...field}
                label={translation.labels.preferredPhoneNumber}
                placeholder={translation.labels.preferredPhoneNumber}
              />
            )}
          />
        </div>
      </div>
      <div className="mg-communication-row content-language">
        <div className="mg-communication-title content-language">{translation.labels.contentLanguagesTitle}</div>
        <div className="mg-communication-description content-language">
          {translation.labels.contentLanguagesDescription}
        </div>
        <Controller
          control={control}
          name="contentLanguages"
          rules={rules.contentLanguage}
          defaultValue={communications.contentLanguages}
          render={({ field, fieldState: { error } }) => (
            <MultiSelect
              {...field}
              selectedOptions={field.value}
              options={languages}
              errorMessage={(error && error.message) || ""}
              label={translation.labels.contentLanguage}
              placeholder={translation.labels.contentLanguage}
            />
          )}
        />
      </div>
      <div className="mg-communication-row language">
        <div className="mg-communication-title language">{translation.labels.languageTitle}</div>
        <div className="mg-communication-description language">{translation.labels.languageDescription}</div>
        <Controller
          control={control}
          name="preferredLanguage"
          defaultValue={communications && communications.preferredLanguage}
          rules={rules.language}
          render={({ field, fieldState: { error } }) => (
            <Select
              id="creator-preferred-language"
              selectedOption={communications && communications.preferredLanguage}
              errorMessage={error && error.message}
              options={locales}
              label={translation.labels.language}
              onChange={(item) => {
                field.onChange(item);
              }}
              dark
            />
          )}
        />
      </div>
    </>
  );
});

export default CommunicationPreferencesInput;
