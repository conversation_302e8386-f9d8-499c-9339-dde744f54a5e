import DashboardFilter from "@components/dashboardFilter/DashboardFilter";
import Loading from "@components/loading/Loading";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import {
  Bar<PERSON><PERSON>,
  CreatorCodeCardV2,
  DashboardStatisticCard,
  EmptyDashboardCard,
  useScreenOrientation
} from "@eait-playerexp-cn/core-ui-kit";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useAppContext } from "@src/context";
import { useDependency } from "@src/context/DependencyContext";
import { RangeFilters } from "@src/server/contentManagement/TimeRangeFilterPageMapper";
import { CreatorCodeStatus } from "@src/server/creators/CreatorWithMultiplePOC";
import CreatorsService from "@src/services/CreatorsService";
import { DashboardChartDetails } from "@src/services/paymentsStatistics/DashboardChartDetails";
import PaymentsService, { Usage } from "@src/services/paymentsStatistics/PaymentsService";
import { LOADING } from "@src/utils";
import React, { FC, useCallback, useEffect, useMemo, useState } from "react";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

export type Labels = {
  product: string;
  myProducts: string;
  codeUsage: string;
  codesUsed: string;
  pending: string;
  screenRotationText: string;
  earningsDisclaimer: string;
  since: string;
  active: string;
  pendingApproval: string;
  totalEarnings: string;
  thisMonth: string;
  earnings: string;
  promotionalEvent: string;
  creatorCode: string;
  codes: string;
  allFilters: string;
  filterby: string;
  startDate: string;
  startDateRequired: string;
  startDateError: string;
  endDate: string;
  endDateRequired: string;
  endDateError: string;
  sameDateError: string;
  range: RangeFilters;
  ok: string;
  cancel: string;
  filteredBy: string;
  dateRange: string;
  paymentStatus: string;
  opportunityType: string;
  applyFilters: string;
  calendar: string;
  months: {
    jan: string;
    feb: string;
    mar: string;
    apr: string;
    may: string;
    jun: string;
    jul: string;
    aug: string;
    sep: string;
    oct: string;
    nov: string;
    dec: string;
  };
  codeInactiveTitle: string;
  codeInactiveDescription: string;
  earningsOverviewTitle: string;
  earningsOverviewDescription1: string;
  earningsOverviewDescription2: string;
  inactive: string;
  codeInactive: string;
  codeInactiveNotifiedTitle: string;
  codeInactiveNotifiedDescription: string;
};
export type DashboardProps = {
  labels: Labels;
  analytics: BrowserAnalytics;
};
const Dashboard: FC<DashboardProps> = ({ labels, analytics }) => {
  const {
    dispatch,
    state: { isLoading }
  } = useAppContext();
  const screenOrientation = useScreenOrientation();
  const stableDispatch = useCallback(dispatch, [isLoading]);
  const {
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE, AFFILIATE_LAUNCH_DATE, BASE_PATH },
    errorHandler,
    client,
    creatorsClient
  } = useDependency();
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedScale, setSelectedScale] = useState("");
  const [chartData, setChartData] = useState<DashboardChartDetails>();
  const [usage, setUsage] = useState<Usage>();
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [isFilter, setIsFilter] = useState(false);
  const [creator, setCreator] = useState(null);
  const [isCreatorCodeActive, setCreatorCodeActive] = useState(false);
  const [creatorCardInfo, setCreatorCardInfo] = useState(null);
  const [emptyCardInfo, setEmptyCardInfo] = useState(null);
  const paymentsService = useMemo(() => new PaymentsService(client), [client]);
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  const initialStartDate = LocalizedDate.startOfMonth().format("MM/DD/YYYY");
  const initialEndDate = LocalizedDate.now().format("MM/DD/YYYY");
  const defaultPaymentDateRange = {
    startDate: LocalizedDate.fromFormattedDate(AFFILIATE_LAUNCH_DATE),
    endDate: LocalizedDate.fromFormattedDate(initialEndDate)
  };
  const criteria = {
    startDate: LocalizedDate.fromFormattedDate(initialStartDate),
    endDate: LocalizedDate.fromFormattedDate(initialEndDate),
    page: 1,
    size: 10
  };
  const [selectedCriteria, setSelectedCriteria] = useState(criteria);

  const paymentsFilterOptions = {
    filterLabels: {
      ...labels,
      filters: labels.allFilters
    },
    dateRangeOptions: [
      { label: labels.range.thisMonth, value: "thisMonth" },
      { label: labels.range.pastMonth, value: "past30Days" },
      { label: labels.range.past90Days, value: "past90Days" },
      { label: labels.range.past6Months, value: "past6Months" },
      { label: labels.range.pastYear, value: "lastYear" }
    ]
  };

  useEffect(() => {
    getPaymentStatistics(initialStartDate, initialEndDate);
    setSelectedDate(
      LocalizedDate.fromFormattedDate(initialStartDate).format("MM/DD/YY") +
        " - " +
        LocalizedDate.fromFormattedDate(initialEndDate).format("MM/DD/YY")
    );
  }, []);

  const updatePaymentsFilterDetails = useCallback((dates) => {
    setSelectedFilters([
      {
        label: dates?.range.label,
        value: dates?.range.value,
        code: "range"
      }
    ]);
    setSelectedCriteria({
      startDate: dates.startDate,
      endDate: dates.endDate,
      page: 1,
      size: 10
    });
    getPaymentStatistics(
      new LocalizedDate(dates.startDate.millisecondsEpoch).format("MM/DD/YYYY"),
      new LocalizedDate(dates.endDate.millisecondsEpoch).format("MM/DD/YYYY")
    );
    setSelectedDate(dates.startDate.format("MM/DD/YY") + " - " + dates.endDate.format("MM/DD/YY"));
    if (dates?.range.value !== "thisMonth") {
      setIsFilter(true);
      setSelectedScale(dates?.range.label);
    } else {
      setIsFilter(false);
      setSelectedScale("");
    }
  }, []);

  const resetFilters = useCallback(() => {
    getPaymentStatistics(initialStartDate, initialEndDate);
    setSelectedFilters([]);
    setSelectedScale("");
    setSelectedCriteria(criteria);
    setIsFilter(false);
    setSelectedDate(
      LocalizedDate.fromFormattedDate(initialStartDate).format("MM/DD/YY") +
        " - " +
        LocalizedDate.fromFormattedDate(initialEndDate).format("MM/DD/YY")
    );
  }, []);

  const getPaymentStatistics = async (startDate: string, endDate: string) => {
    try {
      stableDispatch({ type: LOADING, data: true });
      const creator = FLAG_PER_PROGRAM_PROFILE
        ? await creatorService.getCreator(PROGRAM_CODE)
        : (await CreatorsService.getCreatorWithMultiplePOC(client)).data;

      setCreator(creator);
      updateEmptyCardInfo(creator?.creatorCode?.status as CreatorCodeStatus);
      if (creator?.creatorCode?.status === "ACTIVE") {
        const paymentStatistics: DashboardChartDetails = await paymentsService.getPaymentStatistics({
          creatorCode: creator.creatorCode.code,
          startDate,
          endDate
        });
        setChartData(paymentStatistics);
        setUsage(paymentStatistics.getUsage(labels));
      }
    } catch (error) {
      errorHandler(stableDispatch, error);
    }
    stableDispatch({ type: LOADING, data: false });
  };

  const updateEmptyCardInfo = (status: CreatorCodeStatus) => {
    setCreatorCodeActive(false);
    switch (status) {
      case "ACTIVE":
        setCreatorCardInfo({
          creatorCode: labels.creatorCode,
          status: labels.active
        });
        setEmptyCardInfo(null);
        setCreatorCodeActive(true);
        break;
      case "PENDING":
      case "READYFORAPPROVAL":
        setCreatorCardInfo({
          creatorCode: labels.creatorCode,
          status: labels.pending,
          statusInfo: `*${labels.pendingApproval}`
        });
        setEmptyCardInfo({
          title: labels.codeInactiveNotifiedTitle,
          description: labels.codeInactiveNotifiedDescription
        });
        break;
      case "INACTIVE":
        setCreatorCardInfo({
          creatorCode: labels.creatorCode,
          status: labels.inactive,
          statusInfo: labels.codeInactive
        });
        setEmptyCardInfo({
          title: labels.codeInactiveTitle,
          description: labels.codeInactiveDescription
        });
        break;
    }
  };

  return (
    <div className="dashboard-container">
      {isLoading && (
        <div className="dashboard-loader">
          <Loading />
        </div>
      )}
      {creator && (
        <div>
          <div className="dashboard-creator-code-card">
            <CreatorCodeCardV2
              status={creator?.creatorCode?.status ? creator.creatorCode.status : ""}
              code={isCreatorCodeActive ? creator?.creatorCode?.code : null}
              creatorsGroupImage={"./img/creators-group.png"}
              labels={{ ...creatorCardInfo }}
            />
          </div>
          {emptyCardInfo ? (
            <div className="dashboard-charts-empty-card">
              <EmptyDashboardCard {...emptyCardInfo} />
            </div>
          ) : (
            <div>
              {chartData && usage && (
                <div>
                  {!screenOrientation && (
                    <div className="dashboard-filter">
                      <DashboardFilter
                        dateRangeOptions={paymentsFilterOptions.dateRangeOptions}
                        labels={{
                          ...paymentsFilterOptions.filterLabels,
                          filterPillLabel: selectedScale
                        }}
                        defaultPaymentDateRange={defaultPaymentDateRange}
                        selectedCriteria={selectedCriteria}
                        selectedFilters={selectedFilters}
                        updatePaymentsFilterDetails={updatePaymentsFilterDetails}
                        launchDate={AFFILIATE_LAUNCH_DATE}
                        analytics={analytics}
                        onClickFilterPill={resetFilters}
                        selectedDate={selectedDate}
                        isFilter={isFilter}
                        format="MM/dd/yy"
                        screenOrientation={screenOrientation}
                      />
                    </div>
                  )}
                  <div className="dashboard-charts-container">
                    {chartData && (
                      <div className="dashboard-charts-statistic-card">
                        <DashboardStatisticCard
                          productPieChart={{
                            labels: {
                              myProducts: labels.myProducts,
                              selectedFilter: selectedScale ? `(${selectedScale})` : ""
                            },
                            slices: chartData.getTopProductsDetails() || []
                          }}
                          statisticsCards={[
                            {
                              ...chartData.getEarningsComparison(),
                              labels: {
                                codes: labels.codes,
                                title: labels.earnings
                              }
                            },
                            {
                              ...chartData.getCodesComparison(),
                              labels: {
                                codes: labels.codes,
                                title: labels.codeUsage
                              }
                            }
                          ]}
                        />
                      </div>
                    )}
                    <div className="dashboard-charts-bar-chart">
                      {usage && (
                        <BarChart
                          data={usage.chartData || []}
                          dataLabels={usage.dataLabels || []}
                          labels={labels}
                          legends={usage.legend || []}
                          max={usage.maxValueAndStepSize?.max || 1000}
                          stepSize={usage.maxValueAndStepSize.stepSize || 200}
                          promotionalEventIcon={`${BASE_PATH}/img/Star.svg`}
                        >
                          <div className="dashboard-filter-mobile">
                            <DashboardFilter
                              dateRangeOptions={paymentsFilterOptions.dateRangeOptions}
                              labels={{
                                ...paymentsFilterOptions.filterLabels,
                                filterPillLabel: selectedScale
                              }}
                              defaultPaymentDateRange={defaultPaymentDateRange}
                              selectedCriteria={selectedCriteria}
                              selectedFilters={selectedFilters}
                              updatePaymentsFilterDetails={updatePaymentsFilterDetails}
                              launchDate={AFFILIATE_LAUNCH_DATE}
                              analytics={analytics}
                              onClickFilterPill={resetFilters}
                              selectedDate={selectedDate}
                              isFilter={isFilter}
                              format="MM/dd/yy"
                              screenOrientation={screenOrientation}
                            />
                          </div>
                        </BarChart>
                      )}
                    </div>
                  </div>
                  <div className="dashboard-earnings-disclaimer">{labels.earningsDisclaimer}</div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
