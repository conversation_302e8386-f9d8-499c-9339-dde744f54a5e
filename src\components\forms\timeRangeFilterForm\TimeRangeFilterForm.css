.time-range-filter-button {
  @apply float-right w-fit;
}

.time-range-filter-container {
  @apply relative;
}

.time-range-filter-section {
  @apply fixed bottom-meas0 left-meas0 right-meas0 top-meas0 z-[100] w-full rounded border border-gray-20 bg-[#0D2733] p-meas8 xs:overflow-y-scroll md:absolute md:bottom-auto md:left-auto md:top-[46px] md:w-[340px] md:overflow-y-visible;
}

.time-range-filter-form-elements .select-header-title,
.time-range-filter-form-elements .select-list,
.time-range-filter-form-elements .select-label,
.time-range-filter-section > form {
  @apply w-full text-white;
}
.time-range-filter-footer > button {
  @apply w-full;
}

.time-range-filter-footer > .btn-primary {
  @apply mt-meas10;
}

.time-range-filter-form-elements > .select-box > .select-label {
  @apply text-[12px] font-normal leading-[1rem];
}

.time-range-filter-form-elements > .select-box > .select-header {
  @apply rounded-[4px] border border-gray-20 bg-gray-90;
}

.time-range-filter-form-elements > .select-box > .expanded {
  @apply border-plum-30;
}

.time-range-filter-form-elements > .select-box > .select-header > .select-header-title > .select-header-label {
  @apply text-[12px] leading-[1rem] text-white;
}

.time-range-filter-form-elements > .select-box > .select-header > .select-header-title > .icon {
  @apply text-white;
}

.filter-date-container {
  @apply mt-meas5 block md:grid;
  gap: 12px;
  grid-template-columns: 1fr 1fr;
}

.filter-date-container-label {
  @apply py-meas1 text-[16px] leading-[24px] text-white;
}

.filter-date-container .react-datepicker {
  @apply bg-gray-90 text-white;
}

.filter-date-container .datepicker-ok-option {
  @apply ml-meas3 rounded-[4px] border border-purple-50 bg-purple-50 px-[10px] text-[10px] font-bold leading-[16px]  text-white;
}

.filter-date-container .datepicker-cancel-option {
  @apply text-white;
}

.filter-date-container .datepicker-header {
  @apply bg-purple-50;
}

.filter-date-container .react-datepicker .react-datepicker__children-container {
  @apply w-auto;
}

.filter-date-container .react-datepicker__day--selected .datepicker-day {
  @apply bg-purple-50;
}

.filter-date-container .datepicker-current-month {
  @apply text-white;
}

.filter-date-container .input-text-field {
  @apply bg-[#ffffff00] text-white;
}

.filter-date-container .input-text-field:hover {
  @apply bg-[#ffffff00];
}

.filter-date-container .form-input-box .input-box .input-container {
  @apply rounded-[0px] text-white;
}

.filter-date-container .form-input-box .input-box .input-container:hover {
  @apply bg-[#ffffff00];
}

.filter-date-container .form-input-box .input-box.error.datepicker-error-input {
  @apply border-error-50;
}

.filter-date-container .form-input-box .input-box {
  @apply rounded-[4px] border border-gray-20 bg-gray-90 text-white;
}

.filter-date-container .form-input-box .input-box:hover {
  @apply border-plum-30;
}

.time-range-filter-form-elements > .filter-date-container > .form-input-box > .input-box-label {
  @apply font-normal text-white;
}

.time-range-filter-form-elements .select-list {
  @apply border border-gray-20 bg-[#0D2733] text-white hover:bg-[#0D2733] focus:bg-[#0D2733];
}

.time-range-filter-form-elements .select-list .select-list-item {
  @apply text-white hover:bg-gray-80 focus:bg-gray-80;
}

.time-range-filter-form-elements .select-list .selected.select-list-item {
  @apply bg-gray-80;
}

.time-range-filter-close-button {
  @apply fixed right-meas12 z-[100] block h-[16px] w-[16px] text-white md:hidden;
}

.filter-date-container .react-datepicker__day {
  @apply text-white;
}

.filter-date-container .react-datepicker__day--disabled {
  @apply text-gray-50;
}

.filter-date-container .form-input-box .input-box input:disabled {
  @apply bg-[#ffffff00];
}

.filter-date-container .form-input-box .input-box-disabled:hover {
  @apply border-gray-20;
}
