{"filters": "<PERSON><PERSON><PERSON>", "dateRange": "<PERSON><PERSON>", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "paymentStatus": "Estado del pago", "opportunityType": "Tipo", "applyFilters": "<PERSON><PERSON><PERSON>", "startDateError": "La fecha de inicio debe ser anterior a la fecha de finalización", "endDateError": "La fecha de finalización debe ser posterior a la fecha de inicio", "startDateRequired": "La fecha de inicio es obligatoria", "endDateRequired": "La fecha de finalización es obligatoria", "sameDateError": "Start date and end date cannot be the same", "range": {"allTime": "Hist<PERSON><PERSON><PERSON>", "thisMonth": "<PERSON>ste mes", "past30Days": "Últimos 30 días", "past90Days": "Últimos 90 días", "past6Months": "Últimos 6 meses", "yearToDate": "<PERSON><PERSON> hasta la fecha", "lastYear": "<PERSON><PERSON> pasa<PERSON>", "custom": "Personalización"}, "status": {"all": "Todo", "processed": "Procesado", "pending": "Pendiente"}, "type": {"all": "Todo", "opportunity": "Oportunidad", "creatorCode": "<PERSON><PERSON><PERSON>", "theSims": "<PERSON><PERSON> Sims Maker"}}