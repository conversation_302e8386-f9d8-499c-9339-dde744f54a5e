import { Icon, leftCaret, rightCaret } from "@eait-playerexp-cn/core-ui-kit";
import { useEffect, useState } from "react";
import classNames from "classnames/bind";

export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;

export class Paginator {
  #totalRecords = 0;
  #currentPage = 0;
  #pageSize = 0;
  #totalPages = [];

  constructor(totalRecords, currentPage = DEFAULT_PAGE, pageSize = DEFAULT_PAGE_SIZE) {
    this.#totalRecords = totalRecords;
    this.#currentPage = currentPage;
    this.#pageSize = pageSize;
  }

  #pagesCalculation() {
    const pages = [];
    for (let i = 0; i < Math.ceil(this.#totalRecords / this.#pageSize); i++) {
      pages.push(i + 1);
    }
    return pages;
  }

  hasPages() {
    return this.#totalRecords > DEFAULT_PAGE_SIZE;
  }

  pages() {
    if (!this.#totalPages.length) this.#totalPages = this.#pagesCalculation();

    return this.#totalPages;
  }

  currentPage() {
    return this.#currentPage;
  }
}

const Pagination = ({ next, prev, pages, currentPage, onPageChange }) => {
  const onClickPrev = () => {
    if (currentPage > 1) {
      changePages && setChangePages(false);
      if (pagesInView.indexOf(currentPage) !== 1) {
        if (currentPage > 2) {
          setPagesInView([currentPage - 2, currentPage - 1]);
        } else {
          setPagesInView([currentPage - 1, currentPage]);
        }
      }
      onPageChange(currentPage - 1);
    }
  };

  const onClickNext = () => {
    if (currentPage < pages.length) {
      changePages && setChangePages(false);
      if (pagesInView.indexOf(currentPage) !== -1) {
        if (currentPage < pages.length - 1) {
          setPagesInView([currentPage + 1, currentPage + 2]);
        } else {
          setPagesInView([currentPage, currentPage + 1]);
        }
      }
      onPageChange(currentPage + 1);
    }
  };

  const onClickPage = (page) => {
    if (currentPage !== page) {
      changePages && setChangePages(false);
      if (pagesInView.indexOf(page) !== 0) {
        if (currentPage < pages.length - 1) {
          setPagesInView([page, page + 1]);
        }
      }
      onPageChange(page);
    }
  };
  const [changePages, setChangePages] = useState(true);

  const [pagesInView, setPagesInView] = useState(null);

  useEffect(() => {
    if (currentPage === 1) setChangePages(true);
  }, [currentPage]);

  useEffect(() => {
    if (changePages && pages && currentPage < pages.length) {
      setPagesInView([currentPage, currentPage + 1]);
    } else if (pages && currentPage == pages.length) {
      setPagesInView([currentPage - 1, currentPage]);
    }
  }, [currentPage, changePages]);

  return (
    <>
      {pagesInView && (
        <div className="pagination-container">
          <div className="pagination-text" onClick={onClickPrev}>
            <Icon icon={leftCaret} /> {prev}
          </div>
          <div className="pagination-numbers">
            <span
              data-testid="first-page-number"
              className={classNames(
                {
                  "pagination-text-selected": pagesInView[0] === currentPage
                },
                "pagination-number pagination-text"
              )}
              onClick={() => onClickPage(pagesInView[0])}
            >
              {pagesInView[0]}
            </span>
            <span
              data-testid="second-page-number"
              className={classNames(
                {
                  "pagination-text-selected": pagesInView[1] === currentPage
                },
                "pagination-number pagination-text"
              )}
              onClick={() => onClickPage(pagesInView[1])}
            >
              {pagesInView[1]}
            </span>
          </div>
          <div className="pagination-text" onClick={onClickNext}>
            {next} <Icon icon={rightCaret} />
          </div>
        </div>
      )}
    </>
  );
};
export default Pagination;
