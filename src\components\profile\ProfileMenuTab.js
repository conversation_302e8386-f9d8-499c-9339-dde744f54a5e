import { controller, Icon, Select, user } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import { NO_SHALLOW_ROUTING } from "../../utils";
import SendEmailToPointOfContact from "./SendEmailToPointOfContact";
import Discord from "../icons/ContentCard/Discord";
import { communicationSettings, connectedAccounts, creatorType, legalDocuments } from "../icons/Profile";
import { useEffect, useState } from "react";
import { useDependency } from "../../context/DependencyContext";

const ProfileMenuTab = ({ pocLabels, profileLabels, creator, analytics, buttons }) => {
  const { pathname, query, locale, push } = useRouter();
  const {
    configuration: { FLAG_PER_PROGRAM_PROFILE }
  } = useDependency();

  const menu = [
    { value: "information", label: profileLabels?.information, icon: user },
    { value: "game-preferences", label: profileLabels?.gamePreferences, icon: controller },
    { value: "creator-type", label: profileLabels?.creatorType, icon: creatorType },
    { value: "connected-accounts", label: profileLabels?.connectedAccounts, icon: connectedAccounts },
    {
      value: "communication-settings",
      label: profileLabels?.communicationSettings,
      icon: communicationSettings
    },
    { value: "legal-documents", label: profileLabels?.legalDocuments, icon: legalDocuments }
  ];

  const [selectedValue, setSelectedValue] = useState(
    query.section ? menu.find((menuItem) => [query.section].includes(menuItem.value)) : menu[0]
  );

  useEffect(() => {
    setSelectedValue(query.section ? menu.find((menuItem) => [query.section].includes(menuItem.value)) : menu[0]);
  }, [query.section]);

  return (
    <div className="profile-menu-tab-container">
      <h3 className="profile-menu-tab-title">{profileLabels?.myProfile}</h3>
      <div className="profile-menu-tab-rows">
        <div className="profile-menu-tab-dropdown-container">
          <Select
            id="profile-menu"
            dark
            classes="select-box-dark"
            onChange={(item) => {
              const shallow = NO_SHALLOW_ROUTING.includes(item.value) ? false : true;
              if (item.value === "payment-information") analytics.clickedPaymentInformationInMyProfile({ locale });

              push({ pathname, query: { section: item.value } }, undefined, { shallow });
            }}
            options={menu}
            selectedIcon
            showIcon
            selectedOption={selectedValue}
            skipOnChangeDuringRender
          />
        </div>
        {(FLAG_PER_PROGRAM_PROFILE ? creator?.hasPointOfContact() : creator?.hasPointOfContact) &&
          !creator.accountInformation?.isFlagged && (
            <div className="profile-menu-tab-point-of-contact-card">
              <div>
                <div className="profile-menu-tab-point-of-contact-card-title">
                  {FLAG_PER_PROGRAM_PROFILE ? creator.pointOfContactName() : creator.pointOfContactName}
                </div>
                <div className="profile-menu-tab-point-of-contact-card-tag">{profileLabels?.pointOfContact}</div>
              </div>
              <div>
                <div className="profile-menu-tab-point-of-contact-card-comm">
                  <SendEmailToPointOfContact
                    {...{
                      pocLabels: pocLabels,
                      children: profileLabels?.email,
                      pocName: FLAG_PER_PROGRAM_PROFILE ? creator.pointOfContactName() : creator.pointOfContactName,
                      analytics,
                      buttons,
                      creator
                    }}
                  />
                </div>
                {(FLAG_PER_PROGRAM_PROFILE ? creator.pointOfContactDiscordTag() : creator.pocDiscordTag) && (
                  <div className="profile-menu-tab-point-of-contact-card-comm">
                    <Icon icon={Discord} className="profile-menu-tab-point-of-contact-card-icon" />
                    <span className="profile-menu-tab-point-of-contact-card-comm-text">
                      {FLAG_PER_PROGRAM_PROFILE ? creator.pointOfContactDiscordTag() : creator.pocDiscordTag}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
      </div>
    </div>
  );
};
export default ProfileMenuTab;
