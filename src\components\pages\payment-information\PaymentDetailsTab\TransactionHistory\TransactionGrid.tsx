import { ContentGrid, ContentGridRow } from "@eait-playerexp-cn/core-ui-kit";
import NoTransactionFound from "./NoTransactionFound";
import React, { FC } from "react";
import { DollarAmount } from "@src/services/paymentInformation/DollarAmount";
import Pagination from "../../Pagination";
import classNames from "classnames";
import { ProgramCodeLabelProps, TransactionHistoryLabelProps } from "../PaymentDetailsTab";
import { PaginationProps } from "../PaymentDetailsTab";
import PaymentHistoryDetails from "@src/services/paymentInformation/PaymentHistoryDetails";
import { Transaction } from "@src/services/paymentInformation/Transaction";
import ToolTip from "@components/ToolTip";

export type TransactionGridType = {
  paymentsHistory: PaymentHistoryDetails;
  labels: TransactionHistoryLabelProps;
  isShowingPagination: boolean;
  paginationProps: PaginationProps | null;
  programCodeLabels: ProgramCodeLabelProps;
};

export type TransactionHistoryDataProps = {
  invoiceDate: string;
  programCode: string;
  paymentDescription: string;
  status: string;
  statusUpdatedDate: string;
  amount: string;
  processedDate: Date | string;
};

const TransactionGrid: FC<TransactionGridType> = ({
  paymentsHistory,
  labels,
  isShowingPagination,
  paginationProps,
  programCodeLabels
}) => {
  const getProgramCode = (programCode) => {
    let programCodeLabel = "-";
    switch (programCode) {
      case "affiliate":
        programCodeLabel = programCodeLabels.affiliate;
        break;
      case "creator_network":
        programCodeLabel = programCodeLabels.creatorNetwork;
        break;
      case "sims_creator_program":
        programCodeLabel = programCodeLabels.theSims;
        break;
    }
    return programCodeLabel;
  };

  return (
    <div
      className={classNames("payment-details-transaction-history-grid", {
        "no-records": !paymentsHistory.total,
        "with-pagination": isShowingPagination
      })}
    >
      <ContentGrid
        {...{
          headers: [
            {
              id: "invoiceDate",
              title: labels.paymentPeriodLabel,
              style: { minWidth: "180px" }
            },
            {
              id: "programCode",
              title: labels.paymentGridType,
              style: { minWidth: "140px" },
              renderer: function TransactionType(currentRecord: ContentGridRow) {
                return <div>{getProgramCode(currentRecord.programCode)}</div>;
              }
            },
            {
              id: "paymentDescription",
              title: labels.descriptionLabel,
              style: { minWidth: "140px" },
              renderer: function PaymentDescription(currentRecord: ContentGridRow) {
                const { paymentDescription } = (currentRecord as unknown) as Transaction;
                const title = paymentDescription.length > 20 ? `${paymentDescription.slice(0, 20)}...` : null;
                return (
                  <div className="transaction-history-desc">
                    {title ? <ToolTip overlay={paymentDescription}>{title}</ToolTip> : paymentDescription}
                  </div>
                );
              }
            },
            {
              id: "status",
              title: labels.paymentGridStatus,
              style: { minWidth: "120px" },
              renderer: function TransactionStatus(currentRecord: ContentGridRow) {
                const { status } = currentRecord;
                const statusText =
                  (status as string).toLowerCase() === "pending" ? labels.statusPending : labels.statusProcessed;
                return <span className="transaction-grid-status-text">{statusText as string}</span>;
              }
            },
            {
              id: "statusUpdatedDate",
              title: labels.paymentGridDate,
              style: { minWidth: "140px" }
            },
            {
              id: "amount",
              title: labels.paymentGridAmountDue,
              style: { minWidth: "130px", paddingRight: "0px" },
              renderer: (row: ContentGridRow) => ((row.amount as unknown) as DollarAmount).abbreviateOn("M").toString()
            }
          ],
          customSettings: {
            gridWidth: "",
            noDataFound: (
              <NoTransactionFound message={labels.noPayments}>
                <div className="no-data-found">
                  <span key="key-needed">{labels.noPaymentsDescription}</span>
                </div>
              </NoTransactionFound>
            )
          },
          rows: (paymentsHistory.paymentsHistory as unknown) as ContentGridRow[]
        }}
      />
      {isShowingPagination && <Pagination {...paginationProps} />}
    </div>
  );
};

export default TransactionGrid;
