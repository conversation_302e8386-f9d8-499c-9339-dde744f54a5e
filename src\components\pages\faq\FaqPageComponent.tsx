import React, { memo } from "react";

export type FaqLabelProps = {
  pageTitle: string;
  pageSubtitle: string;
  generalFAQsTitle: string;
  generalFAQsQuestion1: string;
  generalFAQsAnswer1: string;
  generalFAQsQuestion2: string;
  generalFAQsAnswer2: string;
  generalFAQsQuestion3: string;
  generalFAQsAnswer3: string;
  generalFAQsQuestion4: string;
  generalFAQsAnswer4: string;
  eligibilityFAQsTitle: string;
  eligibilityFAQsQuestion1: string;
  eligibilityFAQsAnswer1: string;
  eligibilityFAQsQuestion2: string;
  eligibilityFAQsAnswer2: string;
  eligibilityFAQsQuestion3: string;
  eligibilityFAQsAnswer3: string;
};

export type FaqsPageProps = {
  labels: FaqLabelProps;
};

export default memo(function FaqPageComponent({ labels }: FaqsPageProps) {
  return (
    <div className="faq-page-wrapper">
      <div className="faq-page-header-section">
        <div className="faq-page-heading">{labels.pageTitle}</div>
        <div className="faq-page-subHeading">{labels.pageSubtitle}</div>
      </div>
      <div className="faq-page-content-container">
        <div className="faq-page-content-title faq-page-border">{labels.generalFAQsTitle}</div>
        <div className="faq-page-subContent-wrapper">
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.generalFAQsQuestion1}</div>
            <div className="faq-page-answer">{labels.generalFAQsAnswer1}</div>
          </div>
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.generalFAQsQuestion2}</div>
            <div className="faq-page-answer">{labels.generalFAQsAnswer2}</div>
          </div>
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.generalFAQsQuestion3}</div>
            <div className="faq-page-answer">{labels.generalFAQsAnswer3}</div>
          </div>
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.generalFAQsQuestion4}</div>
            <div className="faq-page-answer faq-page-border">{labels.generalFAQsAnswer4}</div>
          </div>
        </div>
        <div className="faq-page-content-title faq-page-border">{labels.eligibilityFAQsTitle}</div>
        <div className="faq-page-subContent-wrapper">
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.eligibilityFAQsQuestion1}</div>
            <div className="faq-page-answer">{labels.eligibilityFAQsAnswer1}</div>
          </div>
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.eligibilityFAQsQuestion2}</div>
            <div className="faq-page-answer">{labels.eligibilityFAQsAnswer2}</div>
          </div>
          <div className="faq-page-subContent">
            <div className="faq-page-question">{labels.eligibilityFAQsQuestion3}</div>
            <div className="faq-page-answer">{labels.eligibilityFAQsAnswer3}</div>
          </div>
        </div>
      </div>
    </div>
  );
});
