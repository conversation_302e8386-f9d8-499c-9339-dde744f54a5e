import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const France: FC<SvgProps> = ({ className = "icon", ...props }) => {
  const { configuration: config } = useDependency();
  return <img className={className} alt="France flag" src={`${config.BASE_PATH}/img/flags/fr.png`} {...props} />;
};

export default France;
