import { useCallback, useEffect, useMemo, useState } from "react";
import CreatorsService, { CreatorWithCreatorCodeProfile } from "@src/services/CreatorsService";
import { PaymentsCriteria, PaymentStatusType, ProgramCodeType } from "@src/services/paymentsStatistics/PaymentsService";
import { LOADING } from "../../../utils";
import { DEFAULT_PAGE_SIZE } from "./Pagination";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";
import PaymentHistoryDetails from "@src/services/paymentInformation/PaymentHistoryDetails";
import PaymentInformationService from "@src/services/paymentInformation/PaymentInformationService";
import CreatorWithCreatorCode from "@src/server/creators/CreatorWithCreatorCode";
import { CreatorProfile, CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

export type PaymentTabs = "PAYEE_ONBOARDING" | "PAYMENT_HISTORY";
type CreatorPaymentsInformation = {
  id: string;
  businessName: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  email: string;
  zipCode: string;
  stateCode: string;
  city: string;
  countryCode: string;
  addressLine1: string;
  creatorCode: string;
};
export type PaymentsIFrameUrlPayload = CreatorPaymentsInformation & {
  language: string;
  urlType: string;
};
type UsePayableStatus = {
  paymentsIframe: { id?: null | string; embeddableUrl?: null | string };
  isPayable: boolean;
  paymentsHistory: PaymentHistoryDetails;
  setPaymentsHistory: React.Dispatch<React.SetStateAction<PaymentHistoryDetails>>;
  isCreatorCodeAssigned: boolean;
  creator?: CreatorWithCreatorCode | null;
};

export const usePayableStatus = (
  activeTabId: PaymentTabs,
  locale: string,
  stableDispatch: ({ type, data }: { type: string; data: unknown }) => void,
  selectedCriteria: PaymentsCriteria,
  initializePagination: (transactionsCount: number) => void
): UsePayableStatus => {
  const {
    errorHandler,
    client,
    creatorsClient,
    configuration: { PROGRAM_CODE, FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const [paymentsIframe, setPaymentsIframe] = useState<{ id?: null | string; embeddableUrl?: null | string }>({
    id: null,
    embeddableUrl: null
  });
  const [creatorPaymentsInformation, setCreatorPaymentsInformation] = useState<CreatorPaymentsInformation | null>(null);
  const [isPayable, setPayableStatus] = useState<boolean | null>(null);
  const [paymentsHistory, setPaymentsHistory] = useState<PaymentHistoryDetails>({} as PaymentHistoryDetails);
  const paymentInformationService = useMemo(() => new PaymentInformationService(client), [client]);
  const [creator, setCreator] = useState(null);
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  const fetchPaymentsIFrameUrl = async (payload: PaymentsIFrameUrlPayload) => {
    stableDispatch({ type: LOADING, data: true });
    const result = await paymentInformationService.getPaymentsIFrameUrl(payload);
    setPaymentsIframe(result.data);
    stableDispatch({ type: LOADING, data: false });
  };

  const fetchCreator = useCallback(async () => {
    // Creators BFF GET
    try {
      stableDispatch({ type: LOADING, data: true });
      const creator = FLAG_PER_PROGRAM_PROFILE
        ? await creatorService.getCreator(PROGRAM_CODE)
        : (await CreatorsService.getCreatorWithPrograms(client)).data;
      let businessName = "";
      let city = "";
      let countryCode = "";
      let zipCode = "";
      let stateCode = "";
      let addressLine1 = "";

      const legalEntity: {
        businessName?: string;
        city?: string;
        country?: { value: string };
        zipCode?: string;
        stateCode?: string;
        street?: string;
      } = FLAG_PER_PROGRAM_PROFILE
        ? "legalInformation" in creator
          ? creator.legalInformation
          : {
              businessName: "",
              city: "",
              country: { value: "" },
              zipCode: "",
              stateCode: "",
              street: ""
            }
        : "legalEntity" in creator
        ? creator.legalEntity
        : {
            businessName: "",
            city: "",
            country: { value: "" },
            zipCode: "",
            stateCode: "",
            street: ""
          };
      businessName = legalEntity?.businessName || "";
      city = legalEntity.city || "";
      countryCode = legalEntity.country.value || "";
      zipCode = legalEntity.zipCode || "";
      stateCode = legalEntity.stateCode || "";
      addressLine1 = legalEntity.street || "";
      const id = "id" in creator ? creator.id : "";
      const {
        accountInformation: { firstName = "", lastName = "", dateOfBirth = "", originEmail: email = "" }
      } = creator;
      const payable =
        (creator as CreatorProfile).accountInformation?.payable ||
        (creator as CreatorWithCreatorCodeProfile).accountInformation?.isPayable ||
        false;
      const creatorCode =
        (creator as CreatorProfile).creatorCode?.code ||
        (creator as CreatorWithCreatorCodeProfile).accountInformation?.creatorCode ||
        "";
      setCreator(creator);
      const formattedDate: LocalizedDate = (dateOfBirth as unknown) as LocalizedDate;
      setCreatorPaymentsInformation({
        id,
        businessName,
        firstName,
        lastName,
        dateOfBirth: formattedDate.format("YYYY-MM-DD"),
        email,
        zipCode,
        stateCode,
        city,
        countryCode,
        addressLine1,
        creatorCode
      });

      setPayableStatus(payable);
      stableDispatch({ type: LOADING, data: false });
    } catch (error) {
      stableDispatch({ type: LOADING, data: false });
      errorHandler(stableDispatch, error);
    }
  }, [stableDispatch]);

  useEffect(() => {
    setPaymentsIframe({ embeddableUrl: null, id: null });
    fetchCreator();
  }, [fetchCreator]);

  useEffect(() => {
    const language = locale.match(/\w+(?=\-\w+)/g)[0];

    if (activeTabId === "PAYMENT_HISTORY") {
      const { page, startDate, endDate, status = null, programCode = null } = selectedCriteria;

      const criteria: PaymentsCriteria = {
        page,
        size: DEFAULT_PAGE_SIZE,
        startDate: startDate as LocalizedDate,
        endDate: endDate as LocalizedDate
      };

      if (status) criteria["status"] = status as PaymentStatusType;
      if (programCode && programCode !== "ALL") {
        criteria["programCode"] = programCode as ProgramCodeType;
      }

      stableDispatch({ type: LOADING, data: true });

      creatorPaymentsInformation &&
        paymentInformationService
          .getPaymentsHistoryWithProgramCode(criteria)
          .then((response) => {
            stableDispatch({ type: LOADING, data: false });
            setPaymentsHistory(response);
            if (selectedCriteria.page === 1) {
              initializePagination(response.total);
            }
          })
          .catch((error) => {
            stableDispatch({ type: LOADING, data: false });
            errorHandler(stableDispatch, error);
          });
    } else {
      activeTabId &&
        creatorPaymentsInformation &&
        fetchPaymentsIFrameUrl({
          ...creatorPaymentsInformation,
          language,
          urlType: activeTabId
        }).catch((error) => {
          stableDispatch({ type: LOADING, data: false });
          errorHandler(stableDispatch, error);
        });
    }
  }, [activeTabId, creatorPaymentsInformation, selectedCriteria]);

  return {
    paymentsIframe,
    isPayable,
    paymentsHistory,
    setPaymentsHistory,
    isCreatorCodeAssigned: !!creatorPaymentsInformation?.creatorCode,
    creator
  };
};
