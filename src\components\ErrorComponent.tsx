import { memo } from "react";
import { <PERSON>er<PERSON>abels } from "./Layout";
import NavLink from "./header/WebNavLink";

export type ErrorComponentProps = {
  code: number;
  image?: string;
  title: string;
  description: string;
  header: Partial<HeaderLabels>;
};

export default memo(function ErrorComponent({ code, image, title, description, header }: ErrorComponentProps) {
  const styles = image ? { backgroundImage: `url(${image})` } : {};

  return (
    <div className="error-container" style={styles}>
      <div className="error-content-container">
        <h1 className="error-heading">{code}</h1>
        <h2 className="error-title">{title} </h2>
        <div className="error-content">{description}</div>
        <div className="error-nav">
          <span>
            <NavLink title={header.dashboard} href="/dashboard" />
          </span>
          <span>
            <NavLink title={header.home} href="/" />
          </span>
          <span>
            <NavLink title={header.works} href="/#how-it-works" />
          </span>
          <span>
            <NavLink title={header.faqs} href="/faq" />
          </span>
        </div>
      </div>
    </div>
  );
});
