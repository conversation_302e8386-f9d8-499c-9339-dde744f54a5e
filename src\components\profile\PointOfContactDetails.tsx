import React, { FC, useCallback, useState } from "react";
import { PointOfContactCard } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import BrowserAnalytics from "../../analytics/BrowserAnalytics";
import PointOfContactEmailModal from "./PointOfContactEmailModal";

export type PocLabels = {
  buttons: { send: string; cancel: string };
  labels: { body: string; subject: string };
  messages: {
    body: string;
    bodyTooLong: string;
    subject: string;
    subjectTooLong: string;
  };
  subjectPlaceholder: string;
  success: {
    modalHeader: string;
    modalMessage: string;
  };
  title: string;
};

export type ProfileLabels = {
  myProfile: string;
  information: string;
  gamePreferences: string;
  creatorType: string;
  connectedAccounts: string;
  communicationSettings: string;
  legalDocuments: string;
  paymentInformation: string;
  pointOfContact: string;
  discord: string;
  email: string;
};

export type CreatorProps = {
  id: string;
  pointOfContactName: string;
};

export type PointOfContactDetailsProps = {
  pocLabels: PocLabels;
  creator: CreatorProps;
  opportunityId: string;
  profileLabels: ProfileLabels;
  pocName: string;
  title: string;
  pocDiscordTag: string;
  analytics: BrowserAnalytics;
  buttons: { close: string };
  imageUrl?: string;
};

const PointOfContactDetails: FC<PointOfContactDetailsProps> = ({
  pocLabels,
  creator,
  profileLabels,
  pocName,
  title,
  pocDiscordTag,
  analytics,
  buttons,
  imageUrl
}) => {
  const [isShown, setIsShown] = useState(false);
  const router = useRouter();
  const showModal = useCallback(() => {
    analytics.clickedEmailPocLink({ locale: router.locale });
    setIsShown(true);
  }, []);
  const closeModal = useCallback(() => setIsShown(false), []);

  return (
    <div className="poc-container">
      <PointOfContactCard
        {...{
          ...creator,
          title,
          pointOfContactName: pocName,
          pointOfContact: profileLabels.pointOfContact,
          pocDiscordTag,
          pocEmailTag: profileLabels.email,
          imageUrl,
          showEllipsis: true
        }}
        handleClick={showModal}
      />

      {isShown && (
        <PointOfContactEmailModal
          {...{
            pocLabels,
            pocName,
            analytics,
            buttons,
            creator,
            closeModal
          }}
        />
      )}
    </div>
  );
};

export default PointOfContactDetails;
