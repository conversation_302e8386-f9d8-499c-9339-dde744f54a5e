import React, { memo } from "react";
import ProfileMenuWeb from "./profile/ProfileMenuWeb";
import ProfileMenuTab from "./profile/ProfileMenuTab";
import { useDetectScreen } from "../utils";
import { useRouter } from "next/router";
import ProgramSideNavigation from "./ProgramSideNavigation";

export default memo(function ProfileLayout({ children, profileLabels, creator, pocLabels, analytics, buttons }) {
  const isMobileOrTab = useDetectScreen(1279);

  return (
    <>
      <div className="profile-base dark">
        {!isMobileOrTab && (
          <div className="profile-layout-body">
            <div className="profile-base-container-web">
              <ProfileMenuWeb
                {...{
                  profileLabels,
                  pocLabels,
                  creator,
                  analytics,
                  buttons
                }}
              />
              <div className="profile-base-full-screen">{children}</div>
            </div>
          </div>
        )}
        {isMobileOrTab && (
          <div className="profile-base-container-tab">
            <ProfileMenuTab
              {...{
                profileLabels,
                poc<PERSON>abe<PERSON>,
                creator,
                analytics,
                buttons
              }}
            />
            {children}
          </div>
        )}
      </div>
    </>
  );
});
