.creator-display-name-welcome > .tooltip.bottom:after {
  left: 0;
}
.creator-display-name-container {
  @apply inline-flex w-max items-center pb-meas2 pl-meas8 pr-meas8 pt-meas2 font-text-bold text-white;
  border-radius: 9999px;
  border: 1px solid #1e2497;
  background: rgba(13, 16, 66, 0.5);
}
.creator-display-name-division {
  @apply flex items-center justify-center;
}
.creator-display-name-welcome {
  @apply flex w-max max-w-full justify-around text-[0.875rem];
  line-height: 1.25rem;
  color: #969beb;
}
.creator-display-name-username {
  @apply xs:text-mobile-body-large md:text-tablet-h4 lg:text-desktop-h4;
}
.creator-display-name-dashboard {
  margin-top: -0.375rem;
}
.creator-display-name-badge-container {
  @apply ml-meas5 flex items-center justify-center border-l-[1px] pl-meas2 md:pl-meas5;
  border-color: rgba(255, 255, 255, 0.3);
}
.creator-display-name-badge-container .tooltip::after {
  @apply w-meas29 p-meas1;
  background-image: none;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 3px;
}
.creator-display-name-badge-container .tooltip.top::before,
.creator-display-name-badge-container .tooltip.top::after {
  @apply left-[62%];
}
.creator-display-badge {
  @apply w-[1.75rem] md:h-auto md:w-auto;
}
