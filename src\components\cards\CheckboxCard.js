import { Icon } from "@eait-playerexp-cn/core-ui-kit";
import { forwardRef, useCallback, useEffect, useState } from "react";
import { useDependency } from "@src/context/DependencyContext";

const CheckboxCard = ({ disabled, onChange, item, readOnly }, ref) => {
  const { configuration } = useDependency();
  const { label, image, checked, icon, imageAsIcon } = item;
  const styles = image === undefined || image === null || image === "" ? {} : { backgroundImage: `url(${image})` };
  const [selectedClass, setSelectedClass] = useState("");

  useEffect(() => {
    !disabled && setSelectedClass(checked ? " selected-card" : "");
  }, [disabled, checked]);

  const cardOnClick = useCallback(
    (isChecked) => {
      setSelectedClass(isChecked ? " selected-card" : "");
      onChange && onChange(item, isChecked);
    },
    [onChange, item]
  );

  return (
    <div className="card-col" data-disabled={disabled} onChange={(e) => !readOnly && cardOnClick(e.target.checked)}>
      <label className={`check-container${selectedClass}`}>
        <input
          ref={ref}
          data-testid={label}
          aria-label={label}
          type="checkbox"
          defaultChecked={checked}
          disabled={disabled}
        />{" "}
        <span className={`checkmark-layout${selectedClass}`}>&nbsp;</span>
        <span className="checkmark" style={styles} data-disabled={disabled}>
          {icon && <Icon icon={icon} />}
          {imageAsIcon && <img alt="" src={`${configuration.BASE_PATH}${imageAsIcon}`} className="image-as-icon" />}
          <svg
            width="64"
            height="64"
            viewBox="0 0 64 64"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="checkmark-box"
          >
            <path d="M64 64L0 0H64V64Z" fill="currentFillColor" />
            <circle cx="46" cy="19" r="13.25" fill="currentColor" stroke="currentStrokeColor" strokeWidth="1.5" />
          </svg>
        </span>
      </label>
      <div className="checkbox-title">
        <span className="card-title">{label}</span>
      </div>
    </div>
  );
};

export default forwardRef(CheckboxCard);
