import { MobileTransactionHistory } from "@eait-playerexp-cn/core-ui-kit";
import Pagination from "../../Pagination";
import React, { FC } from "react";
import PaymentHistoryDetails from "@src/services/paymentInformation/PaymentHistoryDetails";
import { ProgramCodeLabelProps, TransactionHistoryLabelProps } from "../PaymentDetailsTab";
import classNames from "classnames";
import { PaginationProps } from "../PaymentDetailsTab";
import { Transaction } from "@src/services/paymentInformation/Transaction";

type MobileTransactionGridProps = {
  paymentsHistory: PaymentHistoryDetails;
  labels: TransactionHistoryLabelProps;
  isShowingPagination: boolean;
  paginationProps: PaginationProps;
  programCodeLabels: ProgramCodeLabelProps;
};

const MobileTransactionGrid: FC<MobileTransactionGridProps> = ({
  isShowingPagination,
  paymentsHistory,
  labels,
  paginationProps,
  programCodeLabels
}) => {
  const getProgramCode = (programCode) => {
    let programCodeLabel = "-";
    switch (programCode) {
      case "affiliate":
        programCodeLabel = programCodeLabels.affiliate;
        break;
      case "creator_network":
        programCodeLabel = programCodeLabels.creatorNetwork;
        break;
      case "sims_creator_program":
        programCodeLabel = programCodeLabels.theSims;
        break;
    }
    return programCodeLabel;
  };

  return (
    <div
      className={classNames("payment-details-transaction-history-wrapper mobile-wallet-grid", {
        "with-pagination": isShowingPagination
      })}
    >
      {paymentsHistory.paymentsHistory.map((transaction: Transaction, index) => {
        return (
          <MobileTransactionHistory
            key={index}
            {...{
              opportunityImage: null,
              amount: transaction.amount.abbreviateOn("M").toString(),
              opportunityTitle: null,
              description: (function () {
                const { paymentDescription } = transaction;
                return (
                  <div>
                    {paymentDescription.length > 20 ? `${paymentDescription.slice(0, 20)}...` : paymentDescription}
                  </div>
                );
              })(),
              contractLink: null,
              opportunityType: (function () {
                return <div>{getProgramCode(transaction.programCode)}</div>;
              })(),
              status: (function () {
                const { status } = transaction;
                const statusText =
                  (status as string).toLowerCase() === "pending" ? labels.statusPending : labels.statusProcessed;
                return (
                  <div className={classNames("transaction-grid-icontext-wrapper")}>
                    <span className="transaction-grid-icontext-text">{statusText as string}</span>
                  </div>
                );
              })(),
              processedDate: transaction.statusUpdatedDate,
              paymentPeriod: transaction.invoiceDate,
              labels: {
                opportunityTypeLabel: labels.paymentGridType,
                statusLabel: labels.paymentGridStatus,
                processedDateLabel: labels.paymentGridDate,
                contractLabel: null,
                amountLabel: labels.paymentGridAmountDue,
                linkAriaText: null,
                opportunityImageAltText: null,
                descriptionLabel: labels.descriptionLabel,
                paymentPeriodLabel: labels.paymentPeriodLabel
              },
              onDownloadLinkClickHandler: () => {}
            }}
          />
        );
      })}
      {isShowingPagination && <Pagination {...paginationProps} />}
    </div>
  );
};

export default MobileTransactionGrid;
