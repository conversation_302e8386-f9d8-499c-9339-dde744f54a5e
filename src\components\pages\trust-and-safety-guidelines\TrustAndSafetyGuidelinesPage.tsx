import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import React, { memo } from "react";

type TrustAndSafetyGuidelinesLabelsProps = {
  title: string;
  heading1: string;
  heading2: string;
  heading3: string;
  heading4: string;
  paragraph1: string;
  paragraph2: string;
  paragraph3: string;
  paragraph4: string;
  paragraph5: string;
  paragraph6: string;
  paragraph7: string;
  paragraph8: string;
  paragraphBullet1: string;
  paragraphBullet2: string;
  paragraphBullet3: string;
  paragraphBullet4: string;
};

type TrustAndSafetyGuidelinesPageProps = {
  labels: TrustAndSafetyGuidelinesLabelsProps;
  user?: AuthenticatedUser;
};

export default memo(function TrustAndSafetyGuidelinesPage({ labels, user }: TrustAndSafetyGuidelinesPageProps) {
  return (
    <main
      className={`trust-and-safety-guidelines-parent-container ${
        !user ? "trust-and-safety-guidelines-authenticated" : ""
      }`}
    >
      <div className={`trust-and-safety-guidelines`}>
        <div className="trust-and-safety-guidelines-additional-container">
          <div className="trust-and-safety-guidelines-additional-container-title">{labels.title}</div>
        </div>
      </div>
      <div className="trust-and-safety-guidelines-additional-content-container">
        <div className="trust-and-safety-guidelines-additional-content">
          <span>{labels.paragraph1}</span>
          <span>{labels.paragraph2}</span>
          <span>{labels.paragraph3}</span>

          <h3>{labels.heading1}</h3>
          <span>{labels.paragraph4}</span>
          <ul>
            <li>{labels.paragraphBullet1}</li>
            <li>{labels.paragraphBullet2}</li>
            <li>{labels.paragraphBullet3}</li>
            <li>{labels.paragraphBullet4}</li>
          </ul>
          <span>{labels.paragraph5}</span>

          <h3>{labels.heading2}</h3>
          <span>{labels.paragraph6}</span>

          <h3>{labels.heading3}</h3>
          <span>{labels.paragraph7}</span>

          <h3>{labels.heading4}</h3>
          <span>{labels.paragraph8}</span>
        </div>
      </div>
    </main>
  );
});
