import React, { useCallback, useEffect, useMemo, useState } from "react";
import CreatorTypeForm from "./forms/CreatorTypeForm";
import Form from "../Form";
import CreatorsService from "../../../src/services/CreatorsService";
import Loading from "../Loading";
import FormTitle from "../formTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../../utils";
import { useRouter } from "next/router";
import { useDependency } from "../../context/DependencyContext";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const CreatorType = ({ t, creatorTypeLabels, buttons, creator, updateCreator, creatorTypes, layout, analytics }) => {
  const {
    errorHandler,
    configuration: { FLAG_CREATORS_API_WITH_PROGRAM, FLAG_PER_PROGRAM_PROFILE, PROGRAM_CODE, DEFAULT_AVATAR_IMAGE },
    client,
    creatorsClient
  } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const { dispatch, state: { isValidationError, validationErrors, isError } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const [isCreatorTypeSaved, setIsCreatorTypeSaved] = useState(false);
  const [creatorType, setCreatorType] = useState(null);
  const { success: successToast, error: errorToast } = useToast();
  let modalSuccessText = creatorTypeLabels.successMsgContent;
  const timeToDisplay = Math.min(Math.max(modalSuccessText.length * 50, 2000), 7000);
  const router = useRouter();
  const creatorService = new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE);

  useEffect(() => {
    setCreatorType(creator.creatorTypes);
  }, [creator]);

  const creatorUpdate = useCallback(
    async (data) => {
      // Creators BFF PUT
      try {
        const values = data.creatorTypes.map((item) => item.value);

        setIsCreatorTypeSaved(false);
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateCreator({
              creatorTypes: values,
              program: {
                code: PROGRAM_CODE
              }
            })
          : FLAG_CREATORS_API_WITH_PROGRAM
          ? await CreatorsService.updatePrefferedValues(client, {
              ...data,
              creatorConnectedProgram: PROGRAM_CODE
            })
          : await CreatorsService.update(data);

        analytics.updatedCreatorTypesInProfile({
          locale: router.locale,
          creator,
          selectedCreatorTypes: values
        });
        setCreatorType(values);
        creator.creatorTypes = values;
        updateCreator(creator);
        setIsCreatorTypeSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, updateCreator]
  );
  const { pending: pendingCreatorUpdate, execute: onSubmit } = useAsync(creatorUpdate, false);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(validationErrors)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  useEffect(() => {
    if (!pendingCreatorUpdate && isCreatorTypeSaved) {
      successToast(<Toast header={creatorTypeLabels.successMsgHeader} content={modalSuccessText} />, {
        autoClose: { timetoDisplay: timeToDisplay }
      });
    }
  }, [pendingCreatorUpdate, isCreatorTypeSaved]);
  const creatorOnChange = useCallback(() => setIsCreatorTypeSaved(false), []);

  return (
    <>
      <div className="profile-creator-type">
        <div className="profile-creator-type-title-actions">
          <FormTitle title={creatorTypeLabels.creatorType} />
          <h4 className="profile-creator-type-title">{creatorTypeLabels.title}</h4>
          <div className="profile-creator-type-subtitle">{creatorTypeLabels.infoTitle}</div>
        </div>
        {(!creatorTypes && (
          <div className="loader">
            <Loading />
          </div>
        )) ||
          (creatorTypes && (
            <Form key="creatorType" mode="onChange" onSubmit={onSubmit}>
              <CreatorTypeForm
                {...{
                  t,
                  creatorTypeLabels,
                  creatorTypes,
                  creatorType,
                  buttons,
                  isSaved: isCreatorTypeSaved,
                  isLoader: pendingCreatorUpdate,
                  onChange: creatorOnChange
                }}
              />
            </Form>
          ))}
      </div>
    </>
  );
};
export default CreatorType;
