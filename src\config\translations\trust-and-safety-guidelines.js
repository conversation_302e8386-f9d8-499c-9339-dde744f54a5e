export default function labelsTrustAndSafetyGuidelines(t) {
  return {
    title: t("trust-and-safety-guidelines:title"),
    heading1: t("trust-and-safety-guidelines:heading1"),
    trustAndSafetyGuidelinesPageTitle: t("trust-and-safety-guidelines:trustAndSafetyGuidelinesPageTitle"),
    heading2: t("trust-and-safety-guidelines:heading2"),
    heading3: t("trust-and-safety-guidelines:heading3"),
    heading4: t("trust-and-safety-guidelines:heading4"),
    paragraph1: t("trust-and-safety-guidelines:paragraph1"),
    paragraph2: t("trust-and-safety-guidelines:paragraph2"),
    paragraph3: t("trust-and-safety-guidelines:paragraph3"),
    paragraph4: t("trust-and-safety-guidelines:paragraph4"),
    paragraph5: t("trust-and-safety-guidelines:paragraph5"),
    paragraph6: t("trust-and-safety-guidelines:paragraph6"),
    paragraph7: t("trust-and-safety-guidelines:paragraph7"),
    paragraph8: t("trust-and-safety-guidelines:paragraph8"),
    paragraphBullet1: t("trust-and-safety-guidelines:paragraphBullet1"),
    paragraphBullet2: t("trust-and-safety-guidelines:paragraphBullet2"),
    paragraphBullet3: t("trust-and-safety-guidelines:paragraphBullet3"),
    paragraphBullet4: t("trust-and-safety-guidelines:paragraphBullet4")
  };
}
