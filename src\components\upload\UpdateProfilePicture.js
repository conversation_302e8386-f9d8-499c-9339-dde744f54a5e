import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import { useRouter } from "next/router";
import {
  Button,
  edit,
  Icon,
  ModalBody,
  ModalClose<PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import { SESSION_USER } from "../../utils";
import CreatorsService from "../../../src/services/CreatorsService";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";
import { useDependency } from "@src/context/DependencyContext";

const Avatar = ({ showModal, placeholderRef, src, updateAvatarLabel }) => {
  return (
    <>
      <div className="profile-card-logo">
        <img src={src} alt="Creators Avatar" className="profile-card-avatar" />
      </div>
      <button
        type="button"
        className="profile-card-logo-edit"
        ref={placeholderRef}
        onClick={showModal}
        aria-label={updateAvatarLabel}
      >
        <Icon icon={edit} />
      </button>
    </>
  );
};

const PicturePlaceholder = ({ showModal, placeholderRef, src, updateAvatarLabel }) => {
  return (
    <section className="update-profile-picture-placeholder" ref={placeholderRef}>
      <Avatar {...{ showModal, placeholderRef, src, updateAvatarLabel }} />
    </section>
  );
};

const FooterButtons = memo(function FooterButtons({
  buttons: { save, cancel },
  onClose,
  onSave,
  cancelButtonRef,
  disabled,
  isLoader
}) {
  return (
    <>
      <Button variant="tertiary" dark size="sm" ref={cancelButtonRef} onClick={onClose}>
        {cancel}
      </Button>
      <Button disabled={disabled} spinner={isLoader} size="sm" type="submit" onClick={onSave}>
        {save}
      </Button>
    </>
  );
});

const ModalBodyContent = memo(function ModalBodyContent({
  translate,
  onChange,
  selector,
  previewSrc,
  size,
  invalidImage
}) {
  const { configuration } = useDependency();
  const router = useRouter();
  return (
    <div className="update-profile-picture-container">
      <div className="update-profile-picture-upload-section">
        <div className="update-profile-picture-placeholder-title">{translate.messages.message}</div>
        <button className="btn btn-secondary btn-dark">
          <input type="file" id={selector} name={selector} hidden onChange={onChange} size={size} />
          <label htmlFor={selector} className="update-profile-picture-label">
            {translate.buttons.browse}
          </label>
        </button>
        {invalidImage && <div className="form-error-message">{invalidImage}</div>}
        <div className="update-profile-picture-placeholder-terms">
          {translate.messages.termsAndConditionsFirst}{" "}
          <a
            target="_blank"
            rel="noreferrer"
            href={`${
              router.locale === "en-us"
                ? "https://tos.ea.com/legalapp/WEBTERMS/US/en/PC/"
                : "https://tos.ea.com/legalapp/WEBTERMS/US/ja/PC/"
            }`}
            className="update-profile-picture-agreement-link"
          >
            {translate.messages.termsAndConditionsMiddle}
          </a>{" "}
          {translate.messages.termsAndConditionsLast}
        </div>
      </div>
      <div className="update-profile-picture-image-placeholder">
        <img
          src={`${previewSrc}` || `${configuration.BASE_PATH}/img/migrations/default.png`}
          className="update-profile-picture-preview"
          alt="Avatar Preview"
        />
      </div>
    </div>
  );
});

export const UpdateProfilePicture = ({
  selector = "upload",
  size = 1048576,
  src,
  labels,
  isPlaceholderDefault = true,
  user,
  stableDispatch
}) => {
  const [isShown, setIsShown] = useState(false);
  const [profilePicturePlaceholder, setProfilePicturePlaceholder] = useState(null);
  const [previewSrc, setPreviewSrc] = useState(null);
  const [invalidImage, setInvalidImage] = useState(null);
  const [uploadedSrc, setUploadedSrc] = useState(src);
  const [isFilePicked, setIsFilePicked] = useState(false);
  const [selectedFile, setSelectedFile] = useState();
  const { translate, updateAvatarLabel } = useMemo(() => {
    return {
      translate: { buttons: labels.buttons, messages: labels.profilePicture },
      updateAvatarLabel: labels.profileLabels.updateAvatar
    };
  }, [labels]);
  const {
    creatorsClient,
    configuration: { FLAG_PER_PROGRAM_PROFILE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  const showModal = () => {
    setIsShown(true);
    setPreviewSrc(uploadedSrc);
  };
  const [isLoader, setIsLoader] = useState(false);

  const closeModal = () => {
    setIsShown(false);
    setIsFilePicked(false);
    setPreviewSrc(null);
    setInvalidImage(null);
    profilePicturePlaceholder.focus();
  };
  const changeHandler = (event) => {
    const file = event.target.files[0];
    if (file === undefined) {
      setInvalidImage(translate.messages.avatarRequired);
      return false;
    }
    if (!(file?.type === "image/jpeg" || file?.type === "image/png" || file?.type === "image/gif")) {
      setInvalidImage(translate.messages.avatarInvalid);
      return false;
    }

    if (file?.size > size) {
      setInvalidImage(translate.messages.avatarMoreThanLimit);
      return false;
    }
    setInvalidImage(null);
    setSelectedFile(file);
    setPreviewSrc(URL.createObjectURL(file));
    setIsFilePicked(true);
  };
  const submitHandler = async (event) => {
    event.preventDefault();
    setIsLoader(true);
    if (previewSrc !== null) {
      try {
        const formData = new FormData();
        formData.append("avatar", selectedFile, selectedFile.name);
        FLAG_PER_PROGRAM_PROFILE
          ? await creatorService.updateAvatar(formData)
          : await CreatorsService.updateProfilePicture(formData);

        setUploadedSrc(previewSrc);
        setIsLoader(false);
        closeModal();
        // Update the user prop. So that the new avatar gets rendered across components
        const extension = selectedFile.name.split(".").pop();
        user.avatar =
          user.avatar.split(".").slice(0, -1).join(".").slice(0) + `.${extension}?t=${new Date().getTime()}`;
        stableDispatch({ type: SESSION_USER, data: user });
      } catch (e) {
        setIsLoader(false);
        if (e?.response?.status === 422) {
          setInvalidImage(e.response.data.errors.avatar);
        } else {
          setInvalidImage(e.message);
        }
      }
    }
  };

  const isInvalidFormatPicked = useCallback(() => isFilePicked && invalidImage, [isFilePicked, invalidImage]);
  const cancelButtonRef = useRef(null);

  return (
    <>
      {isPlaceholderDefault && (
        <PicturePlaceholder
          {...{
            showModal,
            src: uploadedSrc,
            placeholderRef: (n) => setProfilePicturePlaceholder(n),
            updateAvatarLabel
          }}
        />
      )}
      {!isPlaceholderDefault && (
        <div className="profile-card-logo-container">
          <Avatar
            {...{
              showModal,
              src: uploadedSrc,
              placeholderRef: (n) => setProfilePicturePlaceholder(n),
              updateAvatarLabel
            }}
          />
        </div>
      )}
      {isShown && (
        <ModalV2 closeButtonRef={cancelButtonRef}>
          <ModalHeader>
            <ModalTitle>{translate.messages.title}</ModalTitle>
            <ModalCloseButton ariaLabel={translate.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <ModalBodyContent {...{ selector, translate, onChange: changeHandler, previewSrc, size, invalidImage }} />
          </ModalBody>
          <ModalFooter showDivider>
            <FooterButtons
              {...{
                buttons: translate.buttons,
                onClose: closeModal,
                onSave: submitHandler,
                disabled: !isFilePicked || isLoader || isInvalidFormatPicked(),
                cancelButtonRef,
                isLoader
              }}
            />
          </ModalFooter>
        </ModalV2>
      )}
    </>
  );
};

export default UpdateProfilePicture;
